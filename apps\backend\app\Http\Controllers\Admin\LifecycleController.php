<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LifecycleRequest;
use App\Http\Resources\Admin\LifecycleResource;
use App\Services\LifecycleService;
use Illuminate\Http\Request;

/**
 * @group 生命周期管理
 *
 * 管理设备生命周期记录
 */
class LifecycleController extends Controller
{
    public function __construct(
        private LifecycleService $lifecycleService
    ) {}

    /**
     * 获取生命周期列表
     *
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page integer 每页数量. Example: 20
     * @queryParam asset_id string 资产ID筛选. Example: 1
     * @queryParam type string 类型筛选. Example: installation
     * @queryParam start_date string 开始日期筛选. Example: 2024-01-01
     * @queryParam end_date string 结束日期筛选. Example: 2024-12-31
     * @queryParam initiator_id integer 发起人ID筛选. Example: 1
     * @queryParam acceptance_entity_id integer 验收相关方ID筛选. Example: 1
     * @queryParam tag_ids string 标签ID逗号分隔. Example: 10,11
     *
     * @apiResourceCollection App\Http\Resources\Admin\LifecycleResource
     *
     * @apiResourceModel App\Models\Lifecycle paginate=20 with=assets:id,name,brand,model,serial_number;initiator:id,nickname;assistants:id,nickname;acceptanceEntity:id,name;acceptancePersonnel:id,name;attachments;tags
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 20);
        $filters = $request->only([
            'asset_id',
            'type',
            'start_date',
            'end_date',
            'initiator_id',
            'acceptance_entity_id',
        ]);

        // 解析标签筛选
        $tagIds = $request->get('tag_ids');
        if (! empty($tagIds)) {
            $ids = is_array($tagIds)
                ? array_map('intval', $tagIds)
                : array_values(array_filter(array_map('intval', explode(',', (string) $tagIds))));
            if (! empty($ids)) {
                $filters['tag_ids'] = $ids;
            }
        }

        $lifecycles = $this->lifecycleService->paginate($filters, $perPage);

        return LifecycleResource::collection($lifecycles);
    }

    /**
     * 获取生命周期详情
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     */
    public function show(int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        return new LifecycleResource($lifecycle);
    }

    /**
     * 创建生命周期
     *
     * @bodyParam assets array required 资产数组. Example: [1,2]
     * @bodyParam type string required 类型（字典值）. Example: installation
     * @bodyParam date string required 日期时间戳. Example: 1716028800
     * @bodyParam initiator_id integer required 发起人ID. Example: 1
     * @bodyParam content string required 内容. Example: 采购了一批新的服务器设备
     * @bodyParam assistants integer[] required 协助人员ID数组. Example: [1]
     * @bodyParam acceptance_entity_id integer required 验收相关方ID. Example: 1
     * @bodyParam acceptance_personnel_id integer required 验收人员ID. Example: 1
     * @bodyParam acceptance_time string required 验收时间戳. Example: 1716028800
     * @bodyParam attachments integer[] 附件ID数组. Example: []
     * @bodyParam tag_ids integer[] 标签ID数组. Example: [17, 18]
     */
    public function store(LifecycleRequest $request)
    {
        $lifecycle = $this->lifecycleService->create($request->validated());

        return new LifecycleResource($lifecycle);
    }

    /**
     * 更新生命周期
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     *
     * @bodyParam assets array 资产数组. Example: [1,2]
     * @bodyParam type string 类型（字典值）. Example: installation
     * @bodyParam date string 日期时间戳. Example: 1716028800
     * @bodyParam initiator_id integer 发起人ID. Example: 1
     * @bodyParam content string 内容. Example: 采购了一批新的服务器设备
     * @bodyParam assistants integer[] 协助人员ID数组. Example: [2, 3]
     * @bodyParam acceptance_entity_id integer 验收相关方ID. Example: 1
     * @bodyParam acceptance_personnel_id integer 验收人员ID. Example: 1
     * @bodyParam acceptance_time string 验收时间戳. Example: 1716028800
     * @bodyParam attachments integer[] 附件ID数组. Example: [1, 2, 3]
     * @bodyParam tag_ids integer[] 标签ID数组. Example: [10, 12]
     */
    public function update(LifecycleRequest $request, int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $lifecycle = $this->lifecycleService->update($lifecycle, $request->validated());

        return new LifecycleResource($lifecycle);
    }

    /**
     * 删除生命周期
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     */
    public function destroy(int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $this->lifecycleService->delete($lifecycle);

        return response()->noContent();
    }

    /**
     * 获取验收人员列表
     *
     * @urlParam entityId integer required 验收相关方ID. Example: 1
     */
    public function getAcceptancePersonnel(int $entityId)
    {
        $personnel = $this->lifecycleService->getAcceptancePersonnel($entityId);

        return response()->json($personnel);
    }

    /**
     * 同步标签
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     *
     * @bodyParam tag_ids integer[] required 标签ID数组. Example: [10, 12]
     */
    public function syncTags(Request $request, int $id)
    {
        $validated = $request->validate([
            'tag_ids' => ['required', 'array'],
            'tag_ids.*' => ['integer', 'exists:tags,id'],
        ]);

        $lifecycle = $this->lifecycleService->find($id);
        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $lifecycle->syncTags($validated['tag_ids']);

        return new LifecycleResource($lifecycle->load(['tags:id,name,category']));
    }

    /**
     * 生命周期验收
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     *
     * @bodyParam is_checked integer required 是否验收. Example: 1
     */
    public function accept(Request $request, int $id)
    {
        $validated = $request->validate([
            'is_checked' => ['required', 'integer'],
        ]);

        $lifecycle = $this->lifecycleService->find($id);
        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        // 判断标签完成情况
        if ($this->lifecycleService->isTagsCompleted($lifecycle)) {
            return $this->error('标签未完成', 400);
        }

        $lifecycle->accept($validated);

        return new LifecycleResource($lifecycle->load(['tags:id,name,category']));
    }
}
