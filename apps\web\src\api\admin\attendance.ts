/**
 * 考勤管理相关接口
 */

import type { PaginatedResponse } from '@/types/api/pagination'
import type {
  CheckinConfig,
  CheckinRecord,
  CheckinStatistics,
  CheckinConfigForm,
  CheckinConfigSearchParams,
  CheckinRecordSearchParams,
  LifecycleOption,
  UserOption
} from '@/types/api/attendance'

// ========== Mock 数据 ==========

// Mock 生命周期项目数据（基于真实场景）
const mockLifecycles: LifecycleOption[] = [
  { id: 1, name: '智慧园区物联网系统部署项目' },
  { id: 2, name: '数字化车间改造实施项目' },
  { id: 3, name: '企业云平台迁移项目' },
  { id: 4, name: '安防监控系统升级项目' },
  { id: 5, name: '智能制造设备集成项目' }
]

// Mock 用户数据
const mockUsers: UserOption[] = [
  { id: 1, name: '张工程师', avatar: '/avatars/user1.jpg' },
  { id: 2, name: '李项目经理', avatar: '/avatars/user2.jpg' },
  { id: 3, name: '王技术员', avatar: '/avatars/user3.jpg' },
  { id: 4, name: '赵实施专员', avatar: '/avatars/user4.jpg' },
  { id: 5, name: '陈系统管理员', avatar: '/avatars/user5.jpg' }
]

// Mock 考勤配置数据
const mockConfigs: CheckinConfig[] = [
  {
    id: 1,
    lifecycle_id: 1,
    lifecycle_name: '智慧园区物联网系统部署项目',
    location_address: '北京市朝阳区望京SOHO T3座',
    location_latitude: 40.006042,
    location_longitude: 116.478901,
    location_radius: 500,
    checkin_time: '08:30',
    participant_ids: [1, 2, 3],
    participants: [mockUsers[0], mockUsers[1], mockUsers[2]],
    require_photo: true,
    status: 'active',
    created_at: '2024-01-15T08:30:00Z',
    updated_at: '2024-01-15T08:30:00Z'
  },
  {
    id: 2,
    lifecycle_id: 2,
    lifecycle_name: '数字化车间改造实施项目',
    location_address: '上海市浦东新区张江高科技园区',
    location_latitude: 31.210393,
    location_longitude: 121.614872,
    location_radius: 300,
    checkin_time: '09:00',
    participant_ids: [2, 3, 4, 5],
    participants: [mockUsers[1], mockUsers[2], mockUsers[3], mockUsers[4]],
    require_photo: false,
    status: 'active',
    created_at: '2024-01-20T09:00:00Z',
    updated_at: '2024-01-20T09:00:00Z'
  },
  {
    id: 3,
    lifecycle_id: 3,
    lifecycle_name: '企业云平台迁移项目',
    location_address: '深圳市南山区科技园南区',
    location_latitude: 22.53705,
    location_longitude: 113.93624,
    location_radius: 400,
    checkin_time: '09:00',
    participant_ids: [1, 4, 5],
    participants: [mockUsers[0], mockUsers[3], mockUsers[4]],
    require_photo: true,
    status: 'inactive',
    created_at: '2024-01-25T10:15:00Z',
    updated_at: '2024-02-01T14:20:00Z'
  }
]

// Mock 打卡记录数据
const mockRecords: CheckinRecord[] = [
  {
    id: 1,
    config_id: 1,
    config_name: '智慧园区项目现场考勤',
    user_id: 1,
    user_name: '张工程师',
    user_avatar: '/avatars/user1.jpg',
    checkin_time: Math.floor(Date.now() / 1000) - 3600, // 1小时前
    checkin_date: '2024-02-20',
    location_address: '北京市朝阳区望京SOHO T3座',
    location_latitude: 40.006042,
    location_longitude: 116.478901,
    status: 0,
    status_label: '正常',
    distance: 50,
    created_at: '2024-02-20T08:45:00Z'
  },
  {
    id: 2,
    config_id: 1,
    config_name: '智慧园区项目现场考勤',
    user_id: 2,
    user_name: '李项目经理',
    user_avatar: '/avatars/user2.jpg',
    checkin_time: Math.floor(Date.now() / 1000) - 7200, // 2小时前
    checkin_date: '2024-02-20',
    location_address: '北京市朝阳区望京SOHO T3座附近',
    location_latitude: 40.0061,
    location_longitude: 116.47895,
    status: 2,
    status_label: '异常',
    distance: 80,
    attachments: [101],
    created_at: '2024-02-20T09:15:00Z'
  },
  {
    id: 3,
    config_id: 2,
    config_name: '车间改造项目驻场考勤',
    user_id: 3,
    user_name: '王技术员',
    user_avatar: '/avatars/user3.jpg',
    checkin_time: Math.floor(Date.now() / 1000) - 14400, // 4小时前
    checkin_date: '2024-02-20',
    location_address: '上海市浦东新区张江高科技园区',
    location_latitude: 31.210393,
    location_longitude: 121.614872,
    status: 0,
    status_label: '正常',
    distance: 25,
    created_at: '2024-02-20T07:50:00Z'
  },
  {
    id: 4,
    config_id: 1,
    config_name: '智慧园区项目现场考勤',
    user_id: 3,
    user_name: '王技术员',
    user_avatar: '/avatars/user3.jpg',
    checkin_time: Math.floor(Date.now() / 1000) - 21600, // 6小时前
    checkin_date: '2024-02-19',
    location_address: '北京市朝阳区望京SOHO T3座',
    location_latitude: 40.006042,
    location_longitude: 116.478901,
    status: 2,
    status_label: '异常',
    distance: 30,
    attachments: [102, 103],
    created_at: '2024-02-19T17:15:00Z'
  },
  {
    id: 5,
    config_id: 2,
    config_name: '车间改造项目驻场考勤',
    user_id: 4,
    user_name: '赵实施专员',
    user_avatar: '/avatars/user4.jpg',
    checkin_time: Math.floor(Date.now() / 1000) - 86400, // 1天前
    checkin_date: '2024-02-19',
    location_address: '上海市浦东新区张江高科技园区',
    location_latitude: 31.210393,
    location_longitude: 121.614872,
    status: 2,
    status_label: '异常',
    distance: 600, // 超出打卡范围
    attachments: [104],
    created_at: '2024-02-19T19:30:00Z'
  }
]

// Mock 统计数据
const mockStatistics: CheckinStatistics = {
  total_configs: 3,
  active_configs: 2,
  total_participants: 5,
  today_checkin_count: 8,
  normal_rate: 75.5,
  exception_count: 2,
  recent_records: mockRecords.slice(0, 5)
}

// ========== API 接口函数 ==========

/**
 * 获取考勤配置列表
 */
export const getCheckinConfigs = (
  params?: CheckinConfigSearchParams
): Promise<PaginatedResponse<CheckinConfig>> => {
  // TODO: 实际对接后端 API
  // return request.get<PaginatedResponse<CheckinConfig>>({
  //   url: '/api/admin/checkin-configs',
  //   params
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockConfigs]

      // 应用搜索过滤
      // keyword 已移除，不再需要
      if (params?.lifecycle_id) {
        filteredData = filteredData.filter((item) => item.lifecycle_id === params.lifecycle_id)
      }
      if (params?.status) {
        filteredData = filteredData.filter((item) => item.status === params.status)
      }

      // 分页处理
      const page = params?.page || 1
      const perPage = params?.per_page || 20
      const total = filteredData.length
      const startIndex = (page - 1) * perPage
      const endIndex = startIndex + perPage
      const data = filteredData.slice(startIndex, endIndex)

      resolve({
        data,
        links: {
          first: null,
          last: null,
          prev: null,
          next: null
        },
        meta: {
          total,
          per_page: perPage,
          current_page: page,
          last_page: Math.ceil(total / perPage),
          from: startIndex + 1,
          to: Math.min(startIndex + perPage, total),
          path: '/api/admin/checkin-configs',
          links: []
        }
      })
    }, 800) // 模拟网络延迟
  })
}

/**
 * 获取考勤配置详情
 */
export const getCheckinConfig = (id: number): Promise<CheckinConfig> => {
  // TODO: 实际对接后端 API
  // return request.get<CheckinConfig>({
  //   url: `/api/admin/checkin-configs/${id}`
  // })

  // Mock 数据实现
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const config = mockConfigs.find((item) => item.id === id)
      if (config) {
        resolve(config)
      } else {
        reject(new Error('考勤配置不存在'))
      }
    }, 500)
  })
}

/**
 * 创建考勤配置
 */
export const createCheckinConfig = (data: CheckinConfigForm): Promise<CheckinConfig> => {
  // TODO: 实际对接后端 API
  // return request.post<CheckinConfig>({
  //   url: '/api/admin/checkin-configs',
  //   data
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      const newConfig: CheckinConfig = {
        id: Date.now(), // 临时ID生成
        ...data,
        lifecycle_name: mockLifecycles.find((l) => l.id === data.lifecycle_id)?.name || '',
        participants: mockUsers.filter((u) => data.participant_ids.includes(u.id)),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      mockConfigs.push(newConfig)
      resolve(newConfig)
    }, 1000)
  })
}

/**
 * 更新考勤配置
 */
export const updateCheckinConfig = (
  id: number,
  data: Partial<CheckinConfigForm>
): Promise<CheckinConfig> => {
  // TODO: 实际对接后端 API
  // return request.put<CheckinConfig>({
  //   url: `/api/admin/checkin-configs/${id}`,
  //   data
  // })

  // Mock 数据实现
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockConfigs.findIndex((item) => item.id === id)
      if (index !== -1) {
        const updatedConfig = {
          ...mockConfigs[index],
          ...data,
          lifecycle_name: data.lifecycle_id
            ? mockLifecycles.find((l) => l.id === data.lifecycle_id)?.name || ''
            : mockConfigs[index].lifecycle_name,
          participants: data.participant_ids
            ? mockUsers.filter((u) => data.participant_ids!.includes(u.id))
            : mockConfigs[index].participants,
          checkin_time: data.checkin_time || mockConfigs[index].checkin_time,
          updated_at: new Date().toISOString()
        }

        mockConfigs[index] = updatedConfig
        resolve(updatedConfig)
      } else {
        reject(new Error('考勤配置不存在'))
      }
    }, 800)
  })
}

/**
 * 删除考勤配置
 */
export const deleteCheckinConfig = (id: number): Promise<void> => {
  // TODO: 实际对接后端 API
  // return request.del<void>({
  //   url: `/api/admin/checkin-configs/${id}`
  // })

  // Mock 数据实现
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockConfigs.findIndex((item) => item.id === id)
      if (index !== -1) {
        mockConfigs.splice(index, 1)
        resolve()
      } else {
        reject(new Error('考勤配置不存在'))
      }
    }, 500)
  })
}

/**
 * 获取打卡记录列表
 */
export const getCheckinRecords = (
  params?: CheckinRecordSearchParams
): Promise<PaginatedResponse<CheckinRecord>> => {
  // TODO: 实际对接后端 API
  // return request.get<PaginatedResponse<CheckinRecord>>({
  //   url: '/api/admin/checkin-records',
  //   params
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockRecords]

      // 应用搜索过滤
      if (params?.config_id) {
        filteredData = filteredData.filter((item) => item.config_id === params.config_id)
      }
      if (params?.user_id) {
        filteredData = filteredData.filter((item) => item.user_id === params.user_id)
      }
      if (params?.status) {
        filteredData = filteredData.filter((item) => item.status === params.status)
      }
      if (params?.start_date) {
        filteredData = filteredData.filter((item) => item.checkin_date >= params.start_date!)
      }
      if (params?.end_date) {
        filteredData = filteredData.filter((item) => item.checkin_date <= params.end_date!)
      }

      // 分页处理
      const page = params?.page || 1
      const perPage = params?.per_page || 20
      const total = filteredData.length
      const startIndex = (page - 1) * perPage
      const endIndex = startIndex + perPage
      const data = filteredData.slice(startIndex, endIndex)

      resolve({
        data,
        links: {
          first: null,
          last: null,
          prev: null,
          next: null
        },
        meta: {
          total,
          per_page: perPage,
          current_page: page,
          last_page: Math.ceil(total / perPage),
          from: startIndex + 1,
          to: Math.min(startIndex + perPage, total),
          path: '/api/admin/checkin-records',
          links: []
        }
      })
    }, 600)
  })
}

/**
 * 获取考勤统计数据
 */
export const getCheckinStatistics = (): Promise<CheckinStatistics> => {
  // TODO: 实际对接后端 API
  // return request.get<CheckinStatistics>({
  //   url: '/api/admin/checkin-statistics'
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockStatistics)
    }, 400)
  })
}

/**
 * 批量删除考勤配置
 * TODO: 根据实际需要添加批量操作接口
 */
export const batchDeleteCheckinConfigs = (ids: number[]): Promise<{ message: string }> => {
  // TODO: 实际对接后端 API
  // return request.post<{ message: string }>({
  //   url: '/api/admin/checkin-configs/batch/destroy',
  //   data: { ids }
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      ids.forEach((id) => {
        const index = mockConfigs.findIndex((item) => item.id === id)
        if (index !== -1) {
          mockConfigs.splice(index, 1)
        }
      })
      resolve({ message: `成功删除${ids.length}个考勤配置` })
    }, 800)
  })
}

/**
 * 获取可选择的生命周期项目列表
 * TODO: 实际应从生命周期管理模块获取数据
 */
export const getLifecycleOptions = (): Promise<LifecycleOption[]> => {
  // TODO: 实际对接生命周期 API
  // return request.get<LifecycleOption[]>({
  //   url: '/api/admin/lifecycles/options'
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockLifecycles)
    }, 300)
  })
}

/**
 * 获取可选择的用户列表（参与人员）
 * TODO: 实际应从用户管理模块获取数据
 */
export const getUserOptions = (): Promise<UserOption[]> => {
  // TODO: 实际对接用户 API
  // return request.get<UserOption[]>({
  //   url: '/api/admin/users/options'
  // })

  // Mock 数据实现
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockUsers)
    }, 300)
  })
}
