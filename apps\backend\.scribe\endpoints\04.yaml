name: 品牌管理
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/brands
    metadata:
      groupName: 品牌管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取系统所有品牌列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      keyword:
        name: keyword
        description: 搜索关键字
        required: false
        example: 华为
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      keyword: 华为
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '[{"id":61,"name":"GE Healthcare","entity_id":61,"entity_name":"\u5e7f\u4e1c\u5b89\u5065\u79d1\u6280\u80a1\u4efd\u6709\u9650\u516c\u53f8","display_name":"\u5e7f\u4e1c\u5b89\u5065\u79d1\u6280\u80a1\u4efd\u6709\u9650\u516c\u53f8 - GE Healthcare","description":"Quidem nostrum qui commodi incidunt iure odit.","sort_order":765},{"id":62,"name":"\u7406\u90a6\u4eea\u5668","entity_id":62,"entity_name":"\u82cf\u5dde\u5965\u666e\u62d3\u6fc0\u5149\u79d1\u6280\u6709\u9650\u516c\u53f8","display_name":"\u82cf\u5dde\u5965\u666e\u62d3\u6fc0\u5149\u79d1\u6280\u6709\u9650\u516c\u53f8 - \u7406\u90a6\u4eea\u5668","description":"Officia est dignissimos neque blanditiis odio veritatis excepturi.","sort_order":721}]'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
