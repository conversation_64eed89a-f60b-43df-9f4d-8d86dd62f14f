## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 分类管理
description: |-

  系统分类管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/categories
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取分类树
      description: 获取所有分类的树形结构数据
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      status:
        name: status
        description: '状态筛选（1启用 0禁用）'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      status: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/categories
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 创建分类
      description: 创建新的分类
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 分类名称
        required: true
        example: 电子设备
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      code:
        name: code
        description: 分类编码
        required: true
        example: electronic
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 父级ID，默认0
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 排序值，默认0
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 状态，默认1
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      remark:
        name: remark
        description: 备注信息
        required: false
        example: 电子设备分类
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 电子设备
      code: electronic
      parent_id: 0
      sort: 0
      status: 1
      remark: 电子设备分类
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/categories/{id}'
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新分类
      description: 更新指定的分类信息
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: 分类ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      category: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 分类名称
        required: true
        example: 电子设备
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      code:
        name: code
        description: 分类编码
        required: true
        example: electronic
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 父级ID
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 排序值
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 状态
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      remark:
        name: remark
        description: 备注信息
        required: false
        example: 电子设备分类
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 电子设备
      code: electronic
      parent_id: 0
      sort: 0
      status: 1
      remark: 电子设备分类
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/categories/{id}'
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 删除分类
      description: 删除指定的分类（分类下存在子分类时无法删除）
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the category.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: 分类ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      category: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/categories/children/{parentId}'
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取子分类
      description: 获取指定分类的直接子分类列表
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      parentId:
        name: parentId
        description: 父级分类ID，传0获取顶级分类
        required: true
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      parentId: 0
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/categories/export/template
    metadata:
      groupName: 分类管理
      groupDescription: |-

        系统分类管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 导出分类Excel模板
      description: 导出分类导入的Excel模板文件
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      with_sample:
        name: with_sample
        description: 是否包含示例数据
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      with_sample: true
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
