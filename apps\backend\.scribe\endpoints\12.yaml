name: 资产管理
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/assets
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取资产列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      name:
        name: name
        description: 资产名称搜索
        required: false
        example: 办公电脑
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      brand_id:
        name: brand_id
        description: 品牌ID搜索
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      serial_number:
        name: serial_number
        description: 序列号搜索
        required: false
        example: ABC123456
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 通用搜索关键词（同时搜索名称、品牌、型号、序列号）
        required: false
        example: 联想
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_category_id:
        name: asset_category_id
        description: 资产分类ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      department_category_id:
        name: department_category_id
        description: 科室分类ID
        required: false
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      industry_category_id:
        name: industry_category_id
        description: 行业分类ID
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_status:
        name: asset_status
        description: 资产状态（字典code）
        required: false
        example: in_use
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_condition:
        name: asset_condition
        description: 成色（字典code）
        required: false
        example: brand_new
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_source:
        name: asset_source
        description: 资产来源（字典code）
        required: false
        example: purchase
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_accessory:
        name: is_accessory
        description: 是否附属设备
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      parent_id:
        name: parent_id
        description: 主设备ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      name: 办公电脑
      brand_id: 1
      serial_number: ABC123456
      keyword: 联想
      asset_category_id: 1
      department_category_id: 2
      industry_category_id: 3
      asset_status: in_use
      asset_condition: brand_new
      asset_source: purchase
      is_accessory: false
      parent_id: 1
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":51,"name":"\u751f\u5316\u5206\u6790\u4eea","brand_id":65,"model":"DG4-944","serial_number":"665a39c0-48af-31f1-a546-aa4f41372488","asset_category_ids":[1,2],"asset_category":[{"id":1,"name":"\u884c\u4e1a\u5206\u7c7b","code":"asset_industry_category"},{"id":2,"name":"\u79d1\u5ba4","code":"asset_department"}],"asset_source":"purchase","asset_status":"scrap_registered","asset_condition":"brand_new","parent_id":null,"children_count":0,"region_code":"430181000000","detailed_address":"4351 Keely Wells","start_date":1584087043,"warranty_period":45,"warranty_alert":56,"maintenance_cycle":138,"expected_years":5,"related_entities":null,"remark":"Molestias fugit deleniti distinctio eum doloremque id.","created_by":1,"updated_by":1,"created_at":1755854536,"updated_at":1755854536},{"id":52,"name":"\u9aa8\u5bc6\u5ea6\u4eea","brand_id":66,"model":"1V91S-321","serial_number":"84614a84-7a78-3276-b72a-735334c9a032","asset_category_ids":[1,2],"asset_category":[{"id":1,"name":"\u884c\u4e1a\u5206\u7c7b","code":"asset_industry_category"},{"id":2,"name":"\u79d1\u5ba4","code":"asset_department"}],"asset_source":"produce","asset_status":"pending_check","asset_condition":"brand_new","parent_id":null,"children_count":0,"region_code":"141024000000","detailed_address":"4161 Bauch Loaf Suite 045","start_date":1416077819,"warranty_period":36,"warranty_alert":68,"maintenance_cycle":63,"expected_years":9,"related_entities":null,"remark":"Ut dicta vitae assumenda consequatur ut et sunt.","created_by":1,"updated_by":1,"created_at":1755854536,"updated_at":1755854536}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/assets
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建资产
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 资产名称
        required: true
        example: 办公台式电脑
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      brand_id:
        name: brand_id
        description: 品牌ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      model:
        name: model
        description: 规格型号
        required: false
        example: 'ThinkCentre M720'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      serial_number:
        name: serial_number
        description: 序列号
        required: false
        example: ABC123456789
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_category_ids:
        name: asset_category_ids
        description: 资产分类ID
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_source:
        name: asset_source
        description: 资产来源（字典code）
        required: false
        example: purchase
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_status:
        name: asset_status
        description: 资产状态（字典code）
        required: false
        example: new_unstocked
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_condition:
        name: asset_condition
        description: 成色（字典code）
        required: false
        example: brand_new
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      parent_id:
        name: parent_id
        description: 主设备ID（附属设备时必填）
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      region_code:
        name: region_code
        description: 地区代码
        required: false
        example: '12'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      detailed_address:
        name: detailed_address
        description: 详细地址
        required: false
        example: XX街道XX号XX大厦
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      start_date:
        name: start_date
        description: 启用日期
        required: false
        example: '2024-01-01'
        type: date
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      warranty_period:
        name: warranty_period
        description: 合同质保期（月）
        required: false
        example: 36
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      warranty_alert:
        name: warranty_alert
        description: 质保期预警（天）
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      maintenance_cycle:
        name: maintenance_cycle
        description: 维护周期（天）
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      expected_years:
        name: expected_years
        description: 预计使用年限（年）
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      related_entities:
        name: related_entities
        description: '相关方信息 JSON数组'
        required: false
        example:
          -
            entity_type: manufacturer
            entity_id: 1
            contact_name: 张三
            contact_phone: '13800138000'
            position: 产品经理
            department: 产品部
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].entity_type':
        name: 'related_entities[].entity_type'
        description: 相关方类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].entity_id':
        name: 'related_entities[].entity_id'
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].contact_name':
        name: 'related_entities[].contact_name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].contact_phone':
        name: 'related_entities[].contact_phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].position':
        name: 'related_entities[].position'
        description: 职位
        required: false
        example: 产品经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].department':
        name: 'related_entities[].department'
        description: 部门
        required: false
        example: 产品部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_accessory:
        name: is_accessory
        description: 是否附属设备
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 办公台式电脑
      brand_id: 1
      model: 'ThinkCentre M720'
      serial_number: ABC123456789
      asset_category_ids:
        - 1
        - 2
        - 3
      asset_source: purchase
      asset_status: new_unstocked
      asset_condition: brand_new
      parent_id: 1
      region_code: '12'
      detailed_address: XX街道XX号XX大厦
      start_date: '2024-01-01'
      warranty_period: 36
      warranty_alert: 30
      maintenance_cycle: 90
      expected_years: 5
      related_entities:
        -
          entity_type: manufacturer
          entity_id: 1
          contact_name: 张三
          contact_phone: '13800138000'
          position: 产品经理
          department: 产品部
      remark: architecto
      attachments:
        - 1
        - 2
        - 3
      is_accessory: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/assets/main-assets
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取可作为主设备的资产列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      exclude_id:
        name: exclude_id
        description: 排除的资产ID（避免自己关联自己）
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 搜索关键词
        required: false
        example: 电脑
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      exclude_id: 5
      keyword: 电脑
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/assets/export-template
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 导出资产模板
      description: 导出包含所有字段的Excel模板文件，用于资产批量导入
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/assets/{id}'
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取资产详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the asset.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      asset:
        name: asset
        description: 资产ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      asset: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/assets/{id}'
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新资产
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the asset.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      asset:
        name: asset
        description: 资产ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      asset: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 资产名称
        required: true
        example: 办公台式电脑
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      brand_id:
        name: brand_id
        description: 品牌ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      model:
        name: model
        description: 规格型号
        required: false
        example: 'ThinkCentre M720'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      serial_number:
        name: serial_number
        description: 序列号
        required: false
        example: ABC123456789
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_category_ids:
        name: asset_category_ids
        description: 资产分类ID
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      asset_source:
        name: asset_source
        description: 资产来源（字典code）
        required: false
        example: purchase
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_status:
        name: asset_status
        description: 资产状态（字典code）
        required: false
        example: new_unstocked
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      asset_condition:
        name: asset_condition
        description: 成色（字典code）
        required: false
        example: brand_new
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      parent_id:
        name: parent_id
        description: 主设备ID（附属设备时必填）
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      region_code:
        name: region_code
        description: 地区代码
        required: false
        example: '12'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      detailed_address:
        name: detailed_address
        description: 详细地址
        required: false
        example: XX街道XX号XX大厦
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      start_date:
        name: start_date
        description: 启用日期
        required: false
        example: '2024-01-01'
        type: date
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      warranty_period:
        name: warranty_period
        description: 合同质保期（月）
        required: false
        example: 36
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      warranty_alert:
        name: warranty_alert
        description: 质保期预警（天）
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      maintenance_cycle:
        name: maintenance_cycle
        description: 维护周期（天）
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      expected_years:
        name: expected_years
        description: 预计使用年限（年）
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      related_entities:
        name: related_entities
        description: '相关方信息 JSON数组'
        required: false
        example:
          -
            entity_type: manufacturer
            entity_id: 1
            contact_name: 张三
            contact_phone: '13800138000'
            position: 产品经理
            department: 产品部
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 附件ID数组
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].entity_type':
        name: 'related_entities[].entity_type'
        description: 相关方类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].entity_id':
        name: 'related_entities[].entity_id'
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'related_entities[].contact_name':
        name: 'related_entities[].contact_name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].contact_phone':
        name: 'related_entities[].contact_phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].position':
        name: 'related_entities[].position'
        description: 职位
        required: false
        example: 产品经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'related_entities[].department':
        name: 'related_entities[].department'
        description: 部门
        required: false
        example: 产品部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_accessory:
        name: is_accessory
        description: 是否附属设备
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 办公台式电脑
      brand_id: 1
      model: 'ThinkCentre M720'
      serial_number: ABC123456789
      asset_category_ids:
        - 1
        - 2
        - 3
      asset_source: purchase
      asset_status: new_unstocked
      asset_condition: brand_new
      parent_id: 1
      region_code: '12'
      detailed_address: XX街道XX号XX大厦
      start_date: '2024-01-01'
      warranty_period: 36
      warranty_alert: 30
      maintenance_cycle: 90
      expected_years: 5
      related_entities:
        -
          entity_type: manufacturer
          entity_id: 1
          contact_name: 张三
          contact_phone: '13800138000'
          position: 产品经理
          department: 产品部
      remark: architecto
      attachments:
        - 1
        - 2
        - 3
      is_accessory: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/assets/batch/copy
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 批量复制资产
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      ids:
        name: ids
        description: 资产ID数组
        required: true
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/assets/batch/destroy
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 批量删除资产
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      ids:
        name: ids
        description: 资产ID数组
        required: true
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/assets/{id}'
    metadata:
      groupName: 资产管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除资产
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the asset.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      asset:
        name: asset
        description: 资产ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      asset: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
