name: 配置管理
description: |-

  系统配置管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/configs
    metadata:
      groupName: 配置管理
      groupDescription: |-

        系统配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取所有配置
      description: 获取系统配置和上传配置
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
               "system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"},
               "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/admin/configs/update
    metadata:
      groupName: 配置管理
      groupDescription: |-

        系统配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新配置
      description: 更新系统配置和上传配置
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      configs:
        name: configs
        description: ''
        required: false
        example: '{"system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"}, "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}}'
        type: 配置数据
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.system.system_name:
        name: configs.system.system_name
        description: 系统网站名称
        required: false
        example: 设备云管理系统
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.system.system_logo:
        name: configs.system.system_logo
        description: 系统网站Logo
        required: false
        example: 'https://example.com/logo.png'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.storage_type:
        name: configs.upload.storage_type
        description: 上传文件存储类型
        required: false
        example: local|alioss|qiniu
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.aliyun_access_key:
        name: configs.upload.aliyun_access_key
        description: 阿里云AccessKey
        required: false
        example: '1234567890'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.aliyun_access_secret:
        name: configs.upload.aliyun_access_secret
        description: 阿里云AccessSecret
        required: false
        example: '1234567890'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.aliyun_bucket:
        name: configs.upload.aliyun_bucket
        description: 阿里云Bucket
        required: false
        example: tc-kyx
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.aliyun_region:
        name: configs.upload.aliyun_region
        description: 阿里云区域
        required: false
        example: oss-cn-hangzhou
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      configs.upload.aliyun_endpoint:
        name: configs.upload.aliyun_endpoint
        description: 阿里云Endpoint
        required: false
        example: oss-cn-hangzhou.aliyuncs.com
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      configs: '{"system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"}, "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}}'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/configs/clear-cache
    metadata:
      groupName: 配置管理
      groupDescription: |-

        系统配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 清除配置缓存
      description: 清除所有配置缓存
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
