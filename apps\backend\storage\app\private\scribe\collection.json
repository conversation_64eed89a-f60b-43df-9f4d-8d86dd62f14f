{"variable": [{"id": "baseUrl", "key": "baseUrl", "type": "string", "name": "string", "value": "http://localhost:8005"}], "info": {"name": "设备云管理系统 API Documentation", "_postman_id": "9df0175d-a02d-4a5a-bca1-bbdb3a9a19e6", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Endpoints", "description": "", "item": [{"name": "统一创建导入任务", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/import/:type/:attachment_id", "query": [], "raw": "{{baseUrl}}/api/admin/import/:type/:attachment_id", "variable": [{"id": "type", "key": "type", "value": "architecto", "description": ""}, {"id": "attachment_id", "key": "attachment_id", "value": "16", "description": "The ID of the attachment."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "路径参数:\n- type: 导入类型，支持 asset/category/entity/user\n- attachment: 附件ID"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "统一查询导入任务状态", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/import/:type/tasks/:task", "query": [], "raw": "{{baseUrl}}/api/admin/import/:type/tasks/:task", "variable": [{"id": "type", "key": "type", "value": "architecto", "description": ""}, {"id": "task", "key": "task", "value": "architecto", "description": "The task."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "路径参数:\n- type: 导入类型\n- task: 任务ID"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "二维码生成", "description": "", "item": [{"name": "生成二维码", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/qrcode/generate", "query": [], "raw": "{{baseUrl}}/api/admin/qrcode/generate"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"content\":\"1222\",\"size\":300,\"color\":\"#ff0000\",\"background_color\":\"#ffffff\",\"margin\":1}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [], "code": 201, "body": "{\n  \"qrcode\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...\"\n}", "name": ""}]}]}, {"name": "分类管理", "description": "\n系统分类管理接口", "item": [{"name": "获取分类树", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories", "query": [{"key": "status", "value": "1", "description": "状态筛选（1启用 0禁用）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/categories?status=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有分类的树形结构数据"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories", "query": [], "raw": "{{baseUrl}}/api/admin/categories"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"电子设备\",\"code\":\"electronic\",\"parent_id\":0,\"sort\":0,\"status\":1,\"remark\":\"电子设备分类\"}"}, "description": "创建新的分类"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/:id", "query": [], "raw": "{{baseUrl}}/api/admin/categories/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "分类ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"电子设备\",\"code\":\"electronic\",\"parent_id\":0,\"sort\":0,\"status\":1,\"remark\":\"电子设备分类\"}"}, "description": "更新指定的分类信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/:id", "query": [], "raw": "{{baseUrl}}/api/admin/categories/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "分类ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的分类（分类下存在子分类时无法删除）"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取子分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/children/:parentId", "query": [], "raw": "{{baseUrl}}/api/admin/categories/children/:parentId", "variable": [{"id": "parentId", "key": "parentId", "value": "0", "description": "父级分类ID，传0获取顶级分类"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取指定分类的直接子分类列表"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "导出分类Excel模板", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/export/template", "query": [{"key": "with_sample", "value": "1", "description": "是否包含示例数据", "disabled": false}], "raw": "{{baseUrl}}/api/admin/categories/export/template?with_sample=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "导出分类导入的Excel模板文件"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "品牌管理", "description": "", "item": [{"name": "获取系统所有品牌列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/brands", "query": [{"key": "keyword", "value": "%E5%8D%8E%E4%B8%BA", "description": "搜索关键字", "disabled": false}], "raw": "{{baseUrl}}/api/admin/brands?keyword=%E5%8D%8E%E4%B8%BA"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "[{\"id\":61,\"name\":\"GE Healthcare\",\"entity_id\":61,\"entity_name\":\"\\u5e7f\\u4e1c\\u5b89\\u5065\\u79d1\\u6280\\u80a1\\u4efd\\u6709\\u9650\\u516c\\u53f8\",\"display_name\":\"\\u5e7f\\u4e1c\\u5b89\\u5065\\u79d1\\u6280\\u80a1\\u4efd\\u6709\\u9650\\u516c\\u53f8 - GE Healthcare\",\"description\":\"Quidem nostrum qui commodi incidunt iure odit.\",\"sort_order\":765},{\"id\":62,\"name\":\"\\u7406\\u90a6\\u4eea\\u5668\",\"entity_id\":62,\"entity_name\":\"\\u82cf\\u5dde\\u5965\\u666e\\u62d3\\u6fc0\\u5149\\u79d1\\u6280\\u6709\\u9650\\u516c\\u53f8\",\"display_name\":\"\\u82cf\\u5dde\\u5965\\u666e\\u62d3\\u6fc0\\u5149\\u79d1\\u6280\\u6709\\u9650\\u516c\\u53f8 - \\u7406\\u90a6\\u4eea\\u5668\",\"description\":\"Officia est dignissimos neque blanditiis odio veritatis excepturi.\",\"sort_order\":721}]", "name": ""}]}]}, {"name": "地区管理", "description": "\n省市区地区数据管理接口", "item": [{"name": "获取地区树形结构", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/tree", "query": [{"key": "deep", "value": "3", "description": "获取的层级深度（可选，默认3级）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/regions/tree?deep=3"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取完整的省市区三级树形结构数据，用于级联选择器"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定父级的子地区", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/children/:parentId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/children/:parentId", "variable": [{"id": "parentId", "key": "parentId", "value": "11", "description": "父级地区ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "用于懒加载获取子地区数据"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "根据地区代码获取完整路径", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/path/:code", "query": [], "raw": "{{baseUrl}}/api/admin/regions/path/:code", "variable": [{"id": "code", "key": "code", "value": "110101000000", "description": "地区代码"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "根据区县代码获取省市区完整路径信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "搜索地区", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/search", "query": [{"key": "keyword", "value": "%E5%8C%97%E4%BA%AC", "description": "搜索关键词", "disabled": false}, {"key": "limit", "value": "20", "description": "返回结果数量限制（默认20，最大50）", "disabled": false}, {"key": "deep", "value": "2", "description": "搜索的层级（0省1市2区县，默认搜索所有层级）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/regions/search?keyword=%E5%8C%97%E4%BA%AC&limit=20&deep=2"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"keyword\":\"bngzmiyvdljnikhw\",\"limit\":22,\"deep\":\"2\"}"}, "description": "根据关键词搜索地区，支持名称和拼音搜索"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取省份列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/provinces", "query": [], "raw": "{{baseUrl}}/api/admin/regions/provinces"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有省级行政区列表"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定省份的城市列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/cities/:provinceId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/cities/:provinceId", "variable": [{"id": "provinceId", "key": "provinceId", "value": "11", "description": "省份ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定城市的区县列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/districts/:cityId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/districts/:cityId", "variable": [{"id": "cityId", "key": "cityId", "value": "1101", "description": "城市ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "字典管理", "description": "\n系统字典配置管理接口", "item": [{"name": "获取字典分类列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories", "query": [{"key": "name", "value": "%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B", "description": "分类名称", "disabled": false}, {"key": "code", "value": "device_type", "description": "分类编码", "disabled": false}, {"key": "is_enabled", "value": "1", "description": "是否启用", "disabled": false}], "raw": "{{baseUrl}}/api/admin/dictionary/categories?name=%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B&code=device_type&is_enabled=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有字典分类，支持条件筛选"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"code\":\"device_type\",\"name\":\"设备类型\",\"description\":\"设备类型分类\",\"sort\":1,\"is_enabled\":false}"}, "description": "创建新的字典分类"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories/:id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "字典分类ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"code\":\"device_type\",\"name\":\"设备类型\",\"description\":\"设备类型分类\",\"sort\":1,\"is_enabled\":false}"}, "description": "更新指定的字典分类信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories/:id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "字典分类ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的字典分类（分类下存在字典项时无法删除）"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取字典项列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items", "query": [{"key": "category_id", "value": "1", "description": "分类ID", "disabled": false}, {"key": "code", "value": "desktop", "description": "字典编码", "disabled": false}, {"key": "value", "value": "%E5%8F%B0%E5%BC%8F%E6%9C%BA", "description": "字典值", "disabled": false}, {"key": "is_enabled", "value": "1", "description": "是否启用", "disabled": false}], "raw": "{{baseUrl}}/api/admin/dictionary/items?category_id=1&code=desktop&value=%E5%8F%B0%E5%BC%8F%E6%9C%BA&is_enabled=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有字典项，支持条件筛选"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"category_id\":1,\"code\":\"desktop\",\"value\":\"台式机\",\"label\":\"台式计算机\",\"sort\":1,\"color\":\"#FF5733\",\"icon\":\"el-icon-monitor\",\"config\":{\"key\":\"value\"},\"remark\":\"用于标识台式计算机类型\",\"is_enabled\":false}"}, "description": "在指定分类下创建新的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items/:id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the item."}, {"id": "item", "key": "item", "value": "1", "description": "字典项ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"category_id\":1,\"code\":\"desktop\",\"value\":\"台式机\",\"label\":\"台式计算机\",\"sort\":1,\"color\":\"#FF5733\",\"icon\":\"el-icon-monitor\",\"config\":{\"key\":\"value\"},\"remark\":\"用于标识台式计算机类型\",\"is_enabled\":false}"}, "description": "更新指定的字典项信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items/:id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the item."}, {"id": "item", "key": "item", "value": "1", "description": "字典项ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "根据分类编码获取字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/code/:categoryCode", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/code/:categoryCode", "variable": [{"id": "categoryCode", "key": "categoryCode", "value": "device_type", "description": "分类编码"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取指定分类编码下的所有启用的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "授权", "description": "\n管理员认证相关接口", "item": [{"name": "登录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/login", "query": [], "raw": "{{baseUrl}}/api/admin/login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"account\":\"***********\",\"password\":\"password123\"}"}, "description": "管理员登录接口，验证用户名和密码后返回访问令牌", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 422, "body": "{\"message\":\"账号或密码错误\",\"errors\":{\"account\":[\"账号或密码错误\"]}}", "name": ""}]}, {"name": "退出登录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/logout", "query": [], "raw": "{{baseUrl}}/api/admin/logout"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "撤销当前访问令牌，退出登录状态"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "操作日志管理", "description": "\n系统操作日志查询接口", "item": [{"name": "获取操作日志列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/operation-logs", "query": [{"key": "user_name", "value": "admin", "description": "用户名搜索", "disabled": false}, {"key": "ip", "value": "***********", "description": "IP地址搜索", "disabled": false}, {"key": "method", "value": "POST", "description": "请求方法筛选", "disabled": false}, {"key": "path", "value": "%2Fapi%2Fusers", "description": "请求路径搜索", "disabled": false}, {"key": "operation_type", "value": "create", "description": "操作类型筛选", "disabled": false}, {"key": "target_type", "value": "<PERSON><PERSON>", "description": "目标类型筛选", "disabled": false}, {"key": "menu_name", "value": "%E8%B5%84%E4%BA%A7%E7%AE%A1%E7%90%86", "description": "菜单名称搜索", "disabled": false}, {"key": "start_time", "value": "2024-01-01+00%3A00%3A00", "description": "开始时间", "disabled": false}, {"key": "end_time", "value": "2024-12-31+23%3A59%3A59", "description": "结束时间", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/operation-logs?user_name=admin&ip=***********&method=POST&path=%2Fapi%2Fusers&operation_type=create&target_type=Asset&menu_name=%E8%B5%84%E4%BA%A7%E7%AE%A1%E7%90%86&start_time=2024-01-01+00%3A00%3A00&end_time=2024-12-31+23%3A59%3A59&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取操作日志详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/operation-logs/:operationLog_id", "query": [], "raw": "{{baseUrl}}/api/admin/operation-logs/:operationLog_id", "variable": [{"id": "operationLog_id", "key": "operationLog_id", "value": "16", "description": "The ID of the operationLog."}, {"id": "id", "key": "id", "value": "1", "description": "日志ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取操作类型统计", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/operation-logs/stats/operation-types", "query": [], "raw": "{{baseUrl}}/api/admin/operation-logs/stats/operation-types"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取菜单操作统计", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/operation-logs/stats/menus", "query": [], "raw": "{{baseUrl}}/api/admin/operation-logs/stats/menus"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "标签管理", "description": "\n管理系统标签库", "item": [{"name": "获取标签列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/tags", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页数量.", "disabled": false}, {"key": "name", "value": "%E9%87%8D%E8%A6%81", "description": "标签名称搜索.", "disabled": false}, {"key": "category", "value": "%E9%87%8D%E8%A6%81", "description": "标签分类筛选.", "disabled": false}], "raw": "{{baseUrl}}/api/admin/tags?page=1&per_page=20&name=%E9%87%8D%E8%A6%81&category=%E9%87%8D%E8%A6%81"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":21,\"name\":\"\\u5f85\\u5de1\\u67e5\\u4fdd\\u517b\",\"category\":\"\\u72b6\\u6001\"},{\"id\":22,\"name\":\"\\u7d27\\u6025\\u7ef4\\u4fee\",\"category\":\"\\u72b6\\u6001\"}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "获取所有标签（用于选择器）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/tags/all", "query": [], "raw": "{{baseUrl}}/api/admin/tags/all"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建标签", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/tags", "query": [], "raw": "{{baseUrl}}/api/admin/tags"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"重要\",\"category\":\"重要\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新标签", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/tags/:id", "query": [], "raw": "{{baseUrl}}/api/admin/tags/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "标签ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"重要\",\"category\":\"重要\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除标签", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/tags/:id", "query": [], "raw": "{{baseUrl}}/api/admin/tags/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "标签ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "生命周期管理", "description": "\n管理设备生命周期记录", "item": [{"name": "获取生命周期列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页数量.", "disabled": false}, {"key": "asset_id", "value": "1", "description": "资产ID筛选.", "disabled": false}, {"key": "type", "value": "installation", "description": "类型筛选.", "disabled": false}, {"key": "start_date", "value": "2024-01-01", "description": "开始日期筛选.", "disabled": false}, {"key": "end_date", "value": "2024-12-31", "description": "结束日期筛选.", "disabled": false}, {"key": "initiator_id", "value": "1", "description": "发起人ID筛选.", "disabled": false}, {"key": "acceptance_entity_id", "value": "1", "description": "验收相关方ID筛选.", "disabled": false}, {"key": "tag_ids", "value": "10%2C11", "description": "标签ID逗号分隔.", "disabled": false}], "raw": "{{baseUrl}}/api/admin/lifecycles?page=1&per_page=20&asset_id=1&type=installation&start_date=2024-01-01&end_date=2024-12-31&initiator_id=1&acceptance_entity_id=1&tag_ids=10%2C11"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":null,\"type\":null,\"date\":null,\"initiator\":null,\"content\":null,\"acceptance_entity\":null,\"acceptance_personnel\":null,\"acceptance_time\":null,\"is_checked\":null,\"follow_ups_tags\":[],\"progress\":0,\"created_at\":null,\"updated_at\":null},{\"id\":null,\"type\":null,\"date\":null,\"initiator\":null,\"content\":null,\"acceptance_entity\":null,\"acceptance_personnel\":null,\"acceptance_time\":null,\"is_checked\":null,\"follow_ups_tags\":[],\"progress\":0,\"created_at\":null,\"updated_at\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"assets\":[1,2],\"type\":\"installation\",\"date\":\"1716028800\",\"initiator_id\":1,\"content\":\"采购了一批新的服务器设备\",\"assistants\":[1],\"acceptance_entity_id\":1,\"acceptance_personnel_id\":1,\"acceptance_time\":\"1716028800\",\"attachments\":[],\"tag_ids\":[17,18]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取生命周期详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"assets\":[1,2],\"type\":\"installation\",\"date\":\"1716028800\",\"initiator_id\":1,\"content\":\"采购了一批新的服务器设备\",\"assistants\":[2,3],\"acceptance_entity_id\":1,\"acceptance_personnel_id\":1,\"acceptance_time\":\"1716028800\",\"attachments\":[1,2,3],\"tag_ids\":[10,12]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "生命周期验收", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id/accept", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id/accept", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"is_checked\":1}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取验收人员列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/entities/:entityId/acceptance-personnel", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/entities/:entityId/acceptance-personnel", "variable": [{"id": "entityId", "key": "entityId", "value": "1", "description": "验收相关方ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "同步标签", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id/tags", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id/tags", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"tag_ids\":[10,12]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"date\":\"2024-01-16\",\"person_id\":2,\"content\":\"已联系供应商确认发货时间\",\"attachments\":[1,2,3],\"tag_id\":10}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取跟进记录详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"date\":\"2024-01-16\",\"person_id\":2,\"content\":\"已联系供应商确认发货时间\",\"attachments\":[1,2,3],\"tag_id\":10}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "用户管理", "description": "", "item": [{"name": "获取用户列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users", "query": [{"key": "keyword", "value": "admin", "description": "搜索关键词（用户名、手机号、邮箱）", "disabled": false}, {"key": "status", "value": "enable", "description": "用户状态（enable/disable）", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/users?keyword=admin&status=enable&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":1,\"account\":\"***********\",\"nickname\":\"admin\",\"email\":null,\"avatar\":null,\"avatar_id\":null,\"status\":\"enable\",\"status_label\":\"\\u542f\\u7528\",\"is_super_admin\":true,\"roles\":[{\"id\":0,\"name\":\"\\u5e73\\u53f0\\u7ba1\\u7406\\u5458\"}],\"created_at\":**********,\"updated_at\":**********},{\"id\":1,\"account\":\"***********\",\"nickname\":\"admin\",\"email\":null,\"avatar\":null,\"avatar_id\":null,\"status\":\"enable\",\"status_label\":\"\\u542f\\u7528\",\"is_super_admin\":true,\"roles\":[{\"id\":0,\"name\":\"\\u5e73\\u53f0\\u7ba1\\u7406\\u5458\"}],\"created_at\":**********,\"updated_at\":**********}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users", "query": [], "raw": "{{baseUrl}}/api/admin/users"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"nickname\":\"testuser\",\"email\":\"<EMAIL>\",\"account\":\"***********\",\"status\":\"enable\",\"avatar_id\":16,\"password\":\"password123\"}"}, "description": ""}, "response": [{"header": [], "code": 201, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"account\": \"***********\",\n    \"nickname\": \"testuser\",\n    \"email\": \"<EMAIL>\"\n  }\n}", "name": ""}]}, {"name": "获取用户详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"nickname\":\"testuser\",\"email\":\"<EMAIL>\",\"account\":\"***********\",\"status\":\"enable\",\"avatar_id\":16,\"password\":\"newpassword\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 204, "body": "{}", "name": ""}]}, {"name": "导出用户Excel模板", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/export/template", "query": [], "raw": "{{baseUrl}}/api/admin/users/export/template"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "导出包含字段：用户账号、密码、用户昵称、邮箱、用户角色"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "为用户分配角色（追加式）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/assign", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/assign", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "同步用户角色（覆盖式）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/sync", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/sync", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "移除用户角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/remove", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/remove", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "相关方管理", "description": "", "item": [{"name": "获取相关方列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities", "query": [{"key": "name", "value": "%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8", "description": "相关方名称搜索", "disabled": false}, {"key": "tax_number", "value": "91110108MA01A2B3C4", "description": "税号搜索", "disabled": false}, {"key": "keywords", "value": "%E7%A7%91%E6%8A%80", "description": "特征词搜索", "disabled": false}, {"key": "keyword", "value": "%E6%B5%8B%E8%AF%95", "description": "通用搜索关键词（同时搜索名称、税号、特征词）", "disabled": false}, {"key": "entity_type", "value": "manufacturer", "description": "相关方类型（字典code）", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/entities?name=%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8&tax_number=91110108MA01A2B3C4&keywords=%E7%A7%91%E6%8A%80&keyword=%E6%B5%8B%E8%AF%95&entity_type=manufacturer&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":63,\"name\":\"\\u5317\\u4eac\\u5eb7\\u6cf0\\u533b\\u7597\\u8bbe\\u5907\\u6709\\u9650\\u516c\\u53f8\",\"tax_number\":\"91090933724500949X\",\"entity_type\":\"manufacturer\",\"address\":\"38862 Ferne Locks Suite 058\\nChristianshire, IA 97161\",\"phone\":\"+12063337339\",\"keywords\":\"deleniti distinctio eum\",\"remark\":\"Id aut libero aliquam veniam.\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********},{\"id\":64,\"name\":\"\\u82cf\\u5dde\\u5965\\u666e\\u62d3\\u6fc0\\u5149\\u79d1\\u6280\\u6709\\u9650\\u516c\\u53f8\",\"tax_number\":\"91445197905807351X\",\"entity_type\":\"supplier\",\"address\":\"2669 <PERSON> Trail\\nBeierburgh, VA 78637\",\"phone\":\"+****************\",\"keywords\":\"ut et recusandae\",\"remark\":\"Rerum ex repellendus assumenda et.\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建相关方", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities", "query": [], "raw": "{{baseUrl}}/api/admin/entities"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"测试科技有限公司\",\"tax_number\":\"91110108MA01A2B3C4\",\"entity_type\":\"manufacturer\",\"address\":\"北京市海淀区中关村大街1号\",\"phone\":\"010-12345678\",\"keywords\":\"科技创新\",\"remark\":\"architecto\",\"contacts\":[\"architecto\"],\"brands\":[\"architecto\"],\"attachments\":[16]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "导出相关方模板", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/export-template", "query": [], "raw": "{{baseUrl}}/api/admin/entities/export-template"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "导出包含所有字段的Excel模板文件，用于相关方批量导入"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取相关方详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新相关方", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"测试科技有限公司\",\"tax_number\":\"91110108MA01A2B3C4\",\"entity_type\":\"manufacturer\",\"address\":\"北京市海淀区中关村大街1号\",\"phone\":\"010-12345678\",\"keywords\":\"科技创新\",\"remark\":\"architecto\",\"contacts\":[\"architecto\"],\"brands\":[\"architecto\"],\"attachments\":[16]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除相关方", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取相关方的联系人列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "10", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts?page=1&per_page=10", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":29,\"entity_id\":65,\"name\":\"<PERSON>\",\"phone\":\"************\",\"position\":\"\\u4e34\\u5e8a\\u5de5\\u7a0b\\u5e08\",\"department\":\"\\u5ba2\\u670d\\u90e8\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********},{\"id\":30,\"entity_id\":66,\"name\":\"Dr. <PERSON>\",\"phone\":\"************\",\"position\":\"\\u8bbe\\u5907\\u79d1\\u5de5\\u7a0b\\u5e08\",\"department\":\"\\u91c7\\u8d2d\\u90e8\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":10,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"张三\",\"phone\":\"***********\",\"position\":\"总经理\",\"department\":\"管理部\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts/:id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "id", "key": "id", "value": "1", "description": "The ID of the contact."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}, {"id": "contact", "key": "contact", "value": "1", "description": "联系人ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"张三\",\"phone\":\"***********\",\"position\":\"总经理\",\"department\":\"管理部\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts/:id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "id", "key": "id", "value": "1", "description": "The ID of the contact."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}, {"id": "contact", "key": "contact", "value": "1", "description": "联系人ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取相关方的品牌列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/brands", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "10", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/brands?page=1&per_page=10", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":63,\"entity_id\":67,\"name\":\"GE Healthcare\",\"logo\":null,\"logo_id\":null,\"description\":\"Quidem nostrum qui commodi incidunt iure odit.\",\"sort_order\":765,\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********},{\"id\":64,\"entity_id\":68,\"name\":\"\\u7406\\u90a6\\u4eea\\u5668\",\"logo\":null,\"logo_id\":null,\"description\":\"Officia est dignissimos neque blanditiis odio veritatis excepturi.\",\"sort_order\":721,\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":10,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建品牌", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/brands", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/brands", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"华为\",\"description\":\"全球领先的信息与通信技术解决方案供应商\",\"logo_id\":1,\"sort_order\":100}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新品牌", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/brands/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/brands/:id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "id", "key": "id", "value": "1", "description": "The ID of the brand."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}, {"id": "brand", "key": "brand", "value": "1", "description": "品牌ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"华为\",\"description\":\"全球领先的信息与通信技术解决方案供应商\",\"logo_id\":1,\"sort_order\":100}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除品牌", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/brands/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/brands/:id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "1", "description": "The ID of the entity."}, {"id": "id", "key": "id", "value": "1", "description": "The ID of the brand."}, {"id": "entity", "key": "entity", "value": "1", "description": "相关方ID"}, {"id": "brand", "key": "brand", "value": "1", "description": "品牌ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "考勤记录", "description": "", "item": [{"name": "获取打卡记录列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-records", "query": [{"key": "config_id", "value": "1", "description": "打卡配置ID", "disabled": false}, {"key": "user_id", "value": "1", "description": "用户ID", "disabled": false}, {"key": "status", "value": "", "description": "状态(0-正常,1-异常)", "disabled": true}, {"key": "date_start", "value": "2025-08-01", "description": "开始日期", "disabled": false}, {"key": "date_end", "value": "2025-08-31", "description": "结束日期", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "15", "description": "每页记录数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/checkin-records?config_id=1&user_id=1&status=&date_start=2025-08-01&date_end=2025-08-31&page=1&per_page=15"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "打卡操作", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-records", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-records"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"checkin_config_id\":1,\"location\":\"公司大门\",\"latitude\":\"39.9042\",\"longitude\":\"116.4074\",\"location_range\":50,\"attachment_id\":1,\"content\":\"正常打卡\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取打卡记录详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-records/:id", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-records/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "记录ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "考勤配置", "description": "", "item": [{"name": "获取考勤配置列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-configs", "query": [{"key": "attachable_type", "value": "lifecycles", "description": "所属模块", "disabled": false}, {"key": "status", "value": "1", "description": "状态(0-禁用,1-启用)", "disabled": false}, {"key": "keyword", "value": "%E6%97%A9%E7%8F%AD", "description": "搜索关键词", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "15", "description": "每页记录数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/checkin-configs?attachable_type=lifecycles&status=1&keyword=%E6%97%A9%E7%8F%AD&page=1&per_page=15"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建考勤配置", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-configs", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-configs"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"attachable_type\":\"lifecycles\",\"attachable_id\":1,\"location\":\"公司大门\",\"longitude\":\"39.9042\",\"latitude\":\"116.4074\",\"checkin_time\":1629936000,\"location_range\":100,\"status\":1,\"is_photo\":1,\"user_ids\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新考勤配置", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-configs/:checkinConfig", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-configs/:checkinConfig", "variable": [{"id": "checkinConfig", "key": "checkinConfig", "value": "1", "description": "配置ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"attachable_type\":\"lifecycles\",\"attachable_id\":1,\"location\":\"公司大门\",\"longitude\":\"39.9042\",\"latitude\":\"116.4074\",\"checkin_time\":1629936000,\"location_range\":100,\"status\":1,\"is_photo\":1,\"user_ids\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取考勤配置详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-configs/:checkinConfig", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-configs/:checkinConfig", "variable": [{"id": "checkinConfig", "key": "checkinConfig", "value": "1", "description": "配置ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "开启/关闭考勤配置", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/checkin-configs/:checkinConfig/switch", "query": [], "raw": "{{baseUrl}}/api/admin/checkin-configs/:checkinConfig/switch", "variable": [{"id": "checkinConfig", "key": "checkinConfig", "value": "1", "description": "配置ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"status\":1}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "菜单管理", "description": "\n系统菜单管理接口", "item": [{"name": "获取菜单列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus", "query": [], "raw": "{{baseUrl}}/api/admin/menus"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构"}, "response": [{"header": [], "code": 200, "body": "{\n  \"menuList\": [\n    {\n      \"id\": 1,\n      \"parent_id\": 0,\n      \"name\": \"User\",\n      \"path\": \"/user\",\n      \"component\": \"User\",\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"label\": \"user\",\n      \"sort\": 1,\n      \"is_hide\": false,\n      \"is_hide_tab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"is_iframe\": false,\n      \"keep_alive\": true,\n      \"is_first_level\": false,\n      \"fixed_tab\": false,\n      \"active_path\": \"/user\",\n      \"is_full_page\": false,\n      \"show_badge\": false,\n      \"show_text_badge\": \"new\",\n      \"status\": true,\n      \"meta\": {\n        \"title\": \"用户管理\",\n        \"icon\": \"user\",\n        \"keepAlive\": true,\n        \"showBadge\": false,\n        \"showTextBadge\": \"new\",\n        \"isHide\": false,\n        \"isHideTab\": false,\n        \"link\": \"https://www.baidu.com\",\n        \"isIframe\": false,\n        \"authList\": [\n          {\n            \"title\": \"用户列表\",\n            \"authMark\": \"user:list\"\n          }\n        ],\n        \"isFirstLevel\": false,\n        \"fixedTab\": false,\n        \"activePath\": \"/user\",\n        \"isFullPage\": false\n      }\n    }\n  ]\n}", "name": ""}]}, {"name": "获取菜单树", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/tree", "query": [], "raw": "{{baseUrl}}/api/admin/menus/tree"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "用于选择父级菜单"}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": [\n    {\n      \"id\": 1,\n      \"parent_id\": 0,\n      \"name\": \"User\",\n      \"path\": \"/user\",\n      \"component\": \"User\",\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"label\": \"user\",\n      \"sort\": 1,\n      \"is_hide\": false,\n      \"is_hide_tab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"is_iframe\": false,\n      \"keep_alive\": true,\n      \"is_first_level\": false,\n      \"fixed_tab\": false,\n      \"active_path\": \"/user\",\n      \"is_full_page\": false,\n      \"show_badge\": false,\n      \"show_text_badge\": \"new\",\n      \"status\": true,\n      \"meta\": {\n        \"title\": \"用户管理\",\n        \"icon\": \"user\",\n        \"keepAlive\": true,\n        \"showBadge\": false,\n        \"showTextBadge\": \"new\",\n        \"isHide\": false,\n        \"isHideTab\": false,\n        \"link\": \"https://www.baidu.com\",\n        \"isIframe\": false,\n        \"authList\": [\n          {\n            \"title\": \"用户列表\",\n            \"authMark\": \"user:list\"\n          }\n        ],\n        \"isFirstLevel\": false,\n        \"fixedTab\": false,\n        \"activePath\": \"/user\",\n        \"isFullPage\": false\n      },\n      \"children\": [\n        {\n          \"id\": 2,\n          \"parent_id\": 1,\n          \"name\": \"UserList\",\n          \"path\": \"/user/list\",\n          \"component\": \"UserList\",\n          \"title\": \"用户列表\",\n          \"icon\": \"user\",\n          \"label\": \"user:list\",\n          \"sort\": 1,\n          \"is_hide\": false,\n          \"is_hide_tab\": false,\n          \"link\": \"https://www.baidu.com\",\n          \"is_iframe\": false,\n          \"keep_alive\": true,\n          \"is_first_level\": false,\n          \"fixed_tab\": false,\n          \"active_path\": \"/user/list\",\n          \"is_full_page\": false,\n          \"show_badge\": false,\n          \"show_text_badge\": \"new\",\n          \"status\": true,\n          \"meta\": {\n            \"title\": \"用户列表\",\n            \"icon\": \"user\",\n            \"keepAlive\": true,\n            \"showBadge\": false,\n            \"showTextBadge\": \"new\",\n            \"isHide\": false,\n            \"isHideTab\": false,\n            \"link\": \"https://www.baidu.com\",\n            \"isIframe\": false,\n            \"authList\": [\n              {\n                \"title\": \"用户列表\",\n                \"authMark\": \"user:list\"\n              }\n            ],\n            \"isFirstLevel\": false,\n            \"fixedTab\": false,\n            \"activePath\": \"/user/list\",\n            \"isFullPage\": false\n          }\n        }\n      ]\n    }\n  ]\n}", "name": ""}]}, {"name": "创建菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus", "query": [], "raw": "{{baseUrl}}/api/admin/menus"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"parent_id\":1,\"name\":\"用户管理\",\"path\":\"\\/user\",\"component\":\"User\",\"title\":\"用户管理\",\"icon\":\"user\",\"label\":\"user\",\"sort\":1,\"is_hide\":false,\"is_hide_tab\":false,\"link\":\"https:\\/\\/www.baidu.com\",\"is_iframe\":false,\"keep_alive\":true,\"is_first_level\":false,\"fixed_tab\":false,\"active_path\":\"\\/user\",\"is_full_page\":false,\"show_badge\":false,\"show_text_badge\":\"new\",\"status\":true,\"permissions\":[{\"title\":\"用户列表\",\"auth_mark\":\"user:list\",\"sort\":1}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"parent_id\": 0,\n    \"name\": \"User\",\n    \"path\": \"/user\",\n    \"component\": \"User\",\n    \"title\": \"用户管理\",\n    \"icon\": \"user\",\n    \"label\": \"user\",\n    \"sort\": 1,\n    \"is_hide\": false,\n    \"is_hide_tab\": false,\n    \"link\": \"https://www.baidu.com\",\n    \"is_iframe\": false,\n    \"keep_alive\": true,\n    \"is_first_level\": false,\n    \"fixed_tab\": false,\n    \"active_path\": \"/user\",\n    \"is_full_page\": false,\n    \"show_badge\": false,\n    \"show_text_badge\": \"new\",\n    \"status\": true,\n    \"meta\": {\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"keepAlive\": true,\n      \"showBadge\": false,\n      \"showTextBadge\": \"new\",\n      \"isHide\": false,\n      \"isHideTab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"isIframe\": false,\n      \"authList\": [\n        {\n          \"title\": \"用户列表\",\n          \"authMark\": \"user:list\"\n        }\n      ],\n      \"isFirstLevel\": false,\n      \"fixedTab\": false,\n      \"activePath\": \"/user\",\n      \"isFullPage\": false\n    }\n  }\n}", "name": ""}]}, {"name": "更新菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/:id", "query": [], "raw": "{{baseUrl}}/api/admin/menus/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the menu."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"parent_id\":1,\"name\":\"用户管理\",\"path\":\"\\/user\",\"component\":\"User\",\"title\":\"用户管理\",\"icon\":\"user\",\"label\":\"user\",\"sort\":1,\"is_hide\":false,\"is_hide_tab\":false,\"link\":\"https:\\/\\/www.baidu.com\",\"is_iframe\":false,\"keep_alive\":true,\"is_first_level\":false,\"fixed_tab\":false,\"active_path\":\"\\/user\",\"is_full_page\":false,\"show_badge\":false,\"show_text_badge\":\"new\",\"status\":true,\"permissions\":[{\"title\":\"用户列表\",\"auth_mark\":\"user:list\",\"sort\":1}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"parent_id\": 0,\n    \"name\": \"User\",\n    \"path\": \"/user\",\n    \"component\": \"User\",\n    \"title\": \"用户管理\",\n    \"icon\": \"user\",\n    \"label\": \"user\",\n    \"sort\": 1,\n    \"is_hide\": false,\n    \"is_hide_tab\": false,\n    \"link\": \"https://www.baidu.com\",\n    \"is_iframe\": false,\n    \"keep_alive\": true,\n    \"is_first_level\": false,\n    \"fixed_tab\": false,\n    \"active_path\": \"/user\",\n    \"is_full_page\": false,\n    \"show_badge\": false,\n    \"show_text_badge\": \"new\",\n    \"status\": true,\n    \"meta\": {\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"keepAlive\": true,\n      \"showBadge\": false,\n      \"showTextBadge\": \"new\",\n      \"isHide\": false,\n      \"isHideTab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"isIframe\": false,\n      \"authList\": [\n        {\n          \"title\": \"用户列表\",\n          \"authMark\": \"user:list\"\n        }\n      ],\n      \"isFirstLevel\": false,\n      \"fixedTab\": false,\n      \"activePath\": \"/user\",\n      \"isFullPage\": false\n    }\n  }\n}", "name": ""}]}, {"name": "删除菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/:id", "query": [], "raw": "{{baseUrl}}/api/admin/menus/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the menu."}, {"id": "menu", "key": "menu", "value": "1", "description": "菜单ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"message\": \"菜单删除成功\"\n}", "name": ""}]}]}, {"name": "角色管理", "description": "\n管理系统角色", "item": [{"name": "获取角色列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles", "query": [{"key": "search", "value": "admin", "description": "搜索关键词（角色名称）.", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数.", "disabled": false}], "raw": "{{baseUrl}}/api/admin/roles?search=admin&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":1,\"name\":\"admin\",\"guard_name\":null,\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\",\"menus\":[{\"menu_id\":1,\"permission_ids\":[]},{\"menu_id\":2,\"permission_ids\":[]},{\"menu_id\":3,\"permission_ids\":[]},{\"menu_id\":4,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":5,\"permission_ids\":[9,6,7,8]},{\"menu_id\":6,\"permission_ids\":[]},{\"menu_id\":7,\"permission_ids\":[10,11,12]},{\"menu_id\":8,\"permission_ids\":[13,14]},{\"menu_id\":9,\"permission_ids\":[]},{\"menu_id\":10,\"permission_ids\":[]},{\"menu_id\":11,\"permission_ids\":[15,16,17]},{\"menu_id\":12,\"permission_ids\":[18,19,20]},{\"menu_id\":13,\"permission_ids\":[21,22,23]},{\"menu_id\":14,\"permission_ids\":[24,25,26]},{\"menu_id\":15,\"permission_ids\":[30,27,28,29]},{\"menu_id\":16,\"permission_ids\":[31,32]},{\"menu_id\":17,\"permission_ids\":[33,34,35]},{\"menu_id\":18,\"permission_ids\":[36,37,38,39]}],\"created_at\":\"2025-08-22T09:22:05.000000Z\",\"updated_at\":\"2025-08-22T09:22:05.000000Z\"},{\"id\":1,\"name\":\"admin\",\"guard_name\":null,\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\",\"menus\":[{\"menu_id\":1,\"permission_ids\":[]},{\"menu_id\":2,\"permission_ids\":[]},{\"menu_id\":3,\"permission_ids\":[]},{\"menu_id\":4,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":5,\"permission_ids\":[9,6,7,8]},{\"menu_id\":6,\"permission_ids\":[]},{\"menu_id\":7,\"permission_ids\":[10,11,12]},{\"menu_id\":8,\"permission_ids\":[13,14]},{\"menu_id\":9,\"permission_ids\":[]},{\"menu_id\":10,\"permission_ids\":[]},{\"menu_id\":11,\"permission_ids\":[15,16,17]},{\"menu_id\":12,\"permission_ids\":[18,19,20]},{\"menu_id\":13,\"permission_ids\":[21,22,23]},{\"menu_id\":14,\"permission_ids\":[24,25,26]},{\"menu_id\":15,\"permission_ids\":[30,27,28,29]},{\"menu_id\":16,\"permission_ids\":[31,32]},{\"menu_id\":17,\"permission_ids\":[33,34,35]},{\"menu_id\":18,\"permission_ids\":[36,37,38,39]}],\"created_at\":\"2025-08-22T09:22:05.000000Z\",\"updated_at\":\"2025-08-22T09:22:05.000000Z\"}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles", "query": [], "raw": "{{baseUrl}}/api/admin/roles"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"管理员\",\"description\":\"系统管理员角色\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取角色详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"管理员\",\"description\":\"系统管理员角色\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "分配菜单权限", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:role_id/menu-permissions/assign", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:role_id/menu-permissions/assign", "variable": [{"id": "role_id", "key": "role_id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"permissions\":[{\"menu_id\":1,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":1,\"permission_ids\":[1,2,3,4,5]}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"message\": \"权限分配成功\"\n}", "name": ""}]}]}, {"name": "资产管理", "description": "", "item": [{"name": "获取资产列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets", "query": [{"key": "name", "value": "%E5%8A%9E%E5%85%AC%E7%94%B5%E8%84%91", "description": "资产名称搜索", "disabled": false}, {"key": "brand_id", "value": "1", "description": "品牌ID搜索", "disabled": false}, {"key": "serial_number", "value": "ABC123456", "description": "序列号搜索", "disabled": false}, {"key": "keyword", "value": "%E8%81%94%E6%83%B3", "description": "通用搜索关键词（同时搜索名称、品牌、型号、序列号）", "disabled": false}, {"key": "asset_category_id", "value": "1", "description": "资产分类ID", "disabled": false}, {"key": "department_category_id", "value": "2", "description": "科室分类ID", "disabled": false}, {"key": "industry_category_id", "value": "3", "description": "行业分类ID", "disabled": false}, {"key": "asset_status", "value": "in_use", "description": "资产状态（字典code）", "disabled": false}, {"key": "asset_condition", "value": "brand_new", "description": "成色（字典code）", "disabled": false}, {"key": "asset_source", "value": "purchase", "description": "资产来源（字典code）", "disabled": false}, {"key": "is_accessory", "value": "", "description": "是否附属设备", "disabled": true}, {"key": "parent_id", "value": "1", "description": "主设备ID", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/assets?name=%E5%8A%9E%E5%85%AC%E7%94%B5%E8%84%91&brand_id=1&serial_number=ABC123456&keyword=%E8%81%94%E6%83%B3&asset_category_id=1&department_category_id=2&industry_category_id=3&asset_status=in_use&asset_condition=brand_new&asset_source=purchase&is_accessory=&parent_id=1&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":51,\"name\":\"\\u751f\\u5316\\u5206\\u6790\\u4eea\",\"brand_id\":65,\"model\":\"DG4-944\",\"serial_number\":\"665a39c0-48af-31f1-a546-aa4f41372488\",\"asset_category_ids\":[1,2],\"asset_category\":[{\"id\":1,\"name\":\"\\u884c\\u4e1a\\u5206\\u7c7b\",\"code\":\"asset_industry_category\"},{\"id\":2,\"name\":\"\\u79d1\\u5ba4\",\"code\":\"asset_department\"}],\"asset_source\":\"purchase\",\"asset_status\":\"scrap_registered\",\"asset_condition\":\"brand_new\",\"parent_id\":null,\"children_count\":0,\"region_code\":\"430181000000\",\"detailed_address\":\"4351 Keely Wells\",\"start_date\":1584087043,\"warranty_period\":45,\"warranty_alert\":56,\"maintenance_cycle\":138,\"expected_years\":5,\"related_entities\":null,\"remark\":\"Molestias fugit deleniti distinctio eum doloremque id.\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********},{\"id\":52,\"name\":\"\\u9aa8\\u5bc6\\u5ea6\\u4eea\",\"brand_id\":66,\"model\":\"1V91S-321\",\"serial_number\":\"84614a84-7a78-3276-b72a-735334c9a032\",\"asset_category_ids\":[1,2],\"asset_category\":[{\"id\":1,\"name\":\"\\u884c\\u4e1a\\u5206\\u7c7b\",\"code\":\"asset_industry_category\"},{\"id\":2,\"name\":\"\\u79d1\\u5ba4\",\"code\":\"asset_department\"}],\"asset_source\":\"produce\",\"asset_status\":\"pending_check\",\"asset_condition\":\"brand_new\",\"parent_id\":null,\"children_count\":0,\"region_code\":\"141024000000\",\"detailed_address\":\"4161 Bauch Loaf Suite 045\",\"start_date\":1416077819,\"warranty_period\":36,\"warranty_alert\":68,\"maintenance_cycle\":63,\"expected_years\":9,\"related_entities\":null,\"remark\":\"Ut dicta vitae assumenda consequatur ut et sunt.\",\"created_by\":1,\"updated_by\":1,\"created_at\":**********,\"updated_at\":**********}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets", "query": [], "raw": "{{baseUrl}}/api/admin/assets"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"办公台式电脑\",\"brand_id\":1,\"model\":\"ThinkCentre M720\",\"serial_number\":\"ABC123456789\",\"asset_category_ids\":[1,2,3],\"asset_source\":\"purchase\",\"asset_status\":\"new_unstocked\",\"asset_condition\":\"brand_new\",\"parent_id\":1,\"region_code\":\"12\",\"detailed_address\":\"XX街道XX号XX大厦\",\"start_date\":\"2024-01-01\",\"warranty_period\":36,\"warranty_alert\":30,\"maintenance_cycle\":90,\"expected_years\":5,\"related_entities\":[{\"entity_type\":\"manufacturer\",\"entity_id\":1,\"contact_name\":\"张三\",\"contact_phone\":\"***********\",\"position\":\"产品经理\",\"department\":\"产品部\"}],\"remark\":\"architecto\",\"attachments\":[1,2,3],\"is_accessory\":false}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取可作为主设备的资产列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/main-assets", "query": [{"key": "exclude_id", "value": "5", "description": "排除的资产ID（避免自己关联自己）", "disabled": false}, {"key": "keyword", "value": "%E7%94%B5%E8%84%91", "description": "搜索关键词", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/assets/main-assets?exclude_id=5&keyword=%E7%94%B5%E8%84%91&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "导出资产模板", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/export-template", "query": [], "raw": "{{baseUrl}}/api/admin/assets/export-template"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "导出包含所有字段的Excel模板文件，用于资产批量导入"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取资产详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"办公台式电脑\",\"brand_id\":1,\"model\":\"ThinkCentre M720\",\"serial_number\":\"ABC123456789\",\"asset_category_ids\":[1,2,3],\"asset_source\":\"purchase\",\"asset_status\":\"new_unstocked\",\"asset_condition\":\"brand_new\",\"parent_id\":1,\"region_code\":\"12\",\"detailed_address\":\"XX街道XX号XX大厦\",\"start_date\":\"2024-01-01\",\"warranty_period\":36,\"warranty_alert\":30,\"maintenance_cycle\":90,\"expected_years\":5,\"related_entities\":[{\"entity_type\":\"manufacturer\",\"entity_id\":1,\"contact_name\":\"张三\",\"contact_phone\":\"***********\",\"position\":\"产品经理\",\"department\":\"产品部\"}],\"remark\":\"architecto\",\"attachments\":[1,2,3],\"is_accessory\":false}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "批量复制资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/batch/copy", "query": [], "raw": "{{baseUrl}}/api/admin/assets/batch/copy"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"ids\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "批量删除资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/batch/destroy", "query": [], "raw": "{{baseUrl}}/api/admin/assets/batch/destroy"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"ids\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "配置管理", "description": "\n系统配置管理接口", "item": [{"name": "获取所有配置", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/configs", "query": [], "raw": "{{baseUrl}}/api/admin/configs"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取系统配置和上传配置"}, "response": [{"header": [], "code": 200, "body": "{\n     \"system\": {\"system_name\": \"设备云管理系统\", \"system_logo\": \"https://example.com/logo.png\"},\n     \"upload\": {\"storage_type\": \"alioss|qiniu|local\", \"aliyun_access_key\": \"1234567890\", \"aliyun_access_secret\": \"1234567890\", \"aliyun_bucket\": \"tc-kyx\", \"aliyun_region\": \"oss-cn-hangzhou\", \"aliyun_endpoint\": \"oss-cn-hangzhou.aliyuncs.com\"}\n}", "name": ""}]}, {"name": "更新配置", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/configs/update", "query": [], "raw": "{{baseUrl}}/api/admin/configs/update"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"configs\":\"{\\\"system\\\": {\\\"system_name\\\": \\\"设备云管理系统\\\", \\\"system_logo\\\": \\\"https:\\/\\/example.com\\/logo.png\\\"}, \\\"upload\\\": {\\\"storage_type\\\": \\\"alioss|qiniu|local\\\", \\\"aliyun_access_key\\\": \\\"1234567890\\\", \\\"aliyun_access_secret\\\": \\\"1234567890\\\", \\\"aliyun_bucket\\\": \\\"tc-kyx\\\", \\\"aliyun_region\\\": \\\"oss-cn-hangzhou\\\", \\\"aliyun_endpoint\\\": \\\"oss-cn-hangzhou.aliyuncs.com\\\"}}\"}"}, "description": "更新系统配置和上传配置"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "清除配置缓存", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/configs/clear-cache", "query": [], "raw": "{{baseUrl}}/api/admin/configs/clear-cache"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "清除所有配置缓存"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "附件管理", "description": "附件的上传、下载、删除等操作", "item": [{"name": "获取附件列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页数量", "disabled": false}, {"key": "file_name", "value": "example.pdf", "description": "文件名（模糊搜索）", "disabled": false}, {"key": "start_time", "value": "2024-01-01", "description": "开始时间", "disabled": false}, {"key": "end_time", "value": "2024-12-31", "description": "结束时间", "disabled": false}], "raw": "{{baseUrl}}/api/admin/attachments?page=1&per_page=20&file_name=example.pdf&start_time=2024-01-01&end_time=2024-12-31"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":\"\",\"file_name\":null,\"file_path\":null,\"file_size\":null,\"mime_type\":null,\"storage_type\":null,\"md5_hash\":null,\"file_url\":\"\",\"formatted_file_size\":\" bytes\",\"created_at\":null,\"updated_at\":null,\"relation_id\":null,\"attachable_type\":null,\"attachable_id\":null,\"category\":null,\"description\":null},{\"id\":\"\",\"file_name\":null,\"file_path\":null,\"file_size\":null,\"mime_type\":null,\"storage_type\":null,\"md5_hash\":null,\"file_url\":\"\",\"formatted_file_size\":\" bytes\",\"created_at\":null,\"updated_at\":null,\"relation_id\":null,\"attachable_type\":null,\"attachable_id\":null,\"category\":null,\"description\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "上传附件（本地上传）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/upload", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/upload"}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "src": [], "type": "file"}]}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取附件详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/:id", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/:id", "variable": [{"id": "id", "key": "id", "value": "16", "description": "附件ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "下载附件", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/:attachment/download", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/:attachment/download", "variable": [{"id": "attachment", "key": "attachment", "value": "16", "description": "The attachment."}, {"id": "id", "key": "id", "value": "16", "description": "附件ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取STS临时凭证", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/sts/credentials", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/sts/credentials"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"filename\":\"example.pdf\",\"filesize\":1024000,\"mime_type\":\"application\\/pdf\",\"md5_hash\":\"5d41402abc4b2a76b9719d911017c592\"}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"quick_upload\": false,\n  \"upload_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n  \"credentials\": {\n    \"AccessKeyId\": \"STS.xxx\",\n    \"AccessKeySecret\": \"xxx\",\n    \"SecurityToken\": \"xxx\",\n    \"Expiration\": \"2025-08-04T12:00:00Z\"\n  },\n  \"region\": \"cn-hangzhou\",\n  \"bucket\": \"my-bucket\",\n  \"endpoint\": \"https://oss-cn-hangzhou.aliyuncs.com\",\n  \"prefix\": \"attachments/\"\n}", "name": ""}]}, {"name": "确认上传完成", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/sts/confirm", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/sts/confirm"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"upload_id\":\"550e8400-e29b-41d4-a716-446655440000\",\"object_key\":\"attachments\\/2025\\/08\\/04\\/xxx.pdf\",\"filename\":\"example.pdf\",\"filesize\":1024000,\"mime_type\":\"application\\/pdf\"}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"id\":\"\",\"file_name\":\"modi.png\",\"file_path\":\"uploads\\/5e4f00df-4238-35bd-9edc-0b98dc359c80.png\",\"file_size\":5161316,\"mime_type\":\"image\\/png\",\"storage_type\":\"local\",\"md5_hash\":null,\"file_url\":\"http:\\/\\/localhost:8005\\/storage\\/uploads\\/5e4f00df-4238-35bd-9edc-0b98dc359c80.png\",\"formatted_file_size\":\"4.92 MB\",\"created_at\":null,\"updated_at\":null,\"relation_id\":null,\"attachable_type\":null,\"attachable_id\":null,\"category\":null,\"description\":null}", "name": ""}]}, {"name": "根据业务ID获取附件列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/by-business", "query": [{"key": "attachable_type", "value": "App%5CModels%5CEntity", "description": "业务类型", "disabled": false}, {"key": "attachable_id", "value": "1", "description": "业务ID", "disabled": false}, {"key": "category", "value": "contract", "description": "附件分类", "disabled": false}], "raw": "{{baseUrl}}/api/admin/attachments/by-business?attachable_type=App%5CModels%5CEntity&attachable_id=1&category=contract"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"attachable_type\":\"architecto\",\"attachable_id\":16,\"category\":\"architecto\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新文件关联描述", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/update-by-relation/:attachmentRelation_id", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/update-by-relation/:attachmentRelation_id", "variable": [{"id": "attachmentRelation_id", "key": "attachmentRelation_id", "value": "16", "description": "The ID of the attachmentRelation."}, {"id": "attachable_relation_id", "key": "attachable_relation_id", "value": "16", "description": "附件关联ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"description\":\"这是我的描述\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "Authorization", "type": "string"}]}}