<?php

namespace App\Services;

use App\Enums\AttachmentCategory;
use App\Models\EntityContact;
use App\Models\Lifecycle;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class LifecycleService
{
    /**
     * 获取生命周期分页列表
     */
    public function paginate(array $filters, int $perPage = 20): LengthAwarePaginator
    {
        $query = Lifecycle::with([
            'assets:id,name,brand_id,model,serial_number',
            'initiator:id,nickname',
            'assistants:id,nickname',
            'acceptanceEntity:id,name',
            'acceptancePersonnel:id,name',
            'attachments',
            'tags:id,name,category',
        ]);

        // 应用筛选条件
        $this->applyFilters($query, $filters);

        return $query->latest()->paginate($perPage);
    }

    /**
     * 应用筛选条件
     */
    private function applyFilters(Builder $query, array $filters): void
    {
        // 按资产ID筛选
        if (! empty($filters['asset_id'])) {
            $query->whereHas('assets', function (Builder $q) use ($filters) {
                $q->where('assets.id', $filters['asset_id']);
            });
        }

        // 按类型筛选
        if (! empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        // 按日期范围筛选
        if (! empty($filters['start_date'])) {
            $query->where('date', '>=', $filters['start_date']);
        }
        if (! empty($filters['end_date'])) {
            $query->where('date', '<=', $filters['end_date']);
        }

        // 按发起人筛选
        if (! empty($filters['initiator_id'])) {
            $query->where('initiator_id', $filters['initiator_id']);
        }

        // 按验收相关方筛选
        if (! empty($filters['acceptance_entity_id'])) {
            $query->where('acceptance_entity_id', $filters['acceptance_entity_id']);
        }

        // 按标签ID筛选（包含所有给定标签ID）
        if (! empty($filters['tag_ids']) && is_array($filters['tag_ids'])) {
            $tagIds = array_values(array_unique(array_map('intval', $filters['tag_ids'])));
            foreach ($tagIds as $tagId) {
                $query->whereHas('tags', function (Builder $q) use ($tagId) {
                    $q->where('tags.id', $tagId);
                });
            }
        }
    }

    /**
     * 获取生命周期详情
     */
    public function find(int $id): ?Lifecycle
    {
        return Lifecycle::with([
            'assets:id,name,brand_id,model,serial_number',
            'initiator:id,nickname',
            'assistants:id,nickname',
            'acceptanceEntity:id,name',
            'acceptancePersonnel:id,name,phone',
            'attachments',
            'tags:id,name,category',
            'followUps' => function ($query) {
                $query->with(['person:id,nickname', 'attachments'])
                    ->orderBy('date', 'desc')
                    ->orderBy('created_at', 'desc');
            },
        ])->find($id);
    }

    /**
     * 创建生命周期
     */
    public function create(array $data): Lifecycle
    {
        return DB::transaction(function () use ($data) {
            // 验证验收人员是否属于验收相关方
            $this->validateAcceptancePersonnel($data['acceptance_entity_id'], $data['acceptance_personnel_id']);

            // 创建生命周期
            $lifecycle = Lifecycle::create([
                'type' => $data['type'],
                'date' => $data['date'],
                'initiator_id' => $data['initiator_id'],
                'content' => $data['content'],
                'acceptance_entity_id' => $data['acceptance_entity_id'],
                'acceptance_personnel_id' => $data['acceptance_personnel_id'],
                'acceptance_time' => $data['acceptance_time'],
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // 同步资产
            if (! empty($data['assets'])) {
                $lifecycle->syncAssets($data['assets']);
            }

            // 同步协助人员
            if (isset($data['assistants'])) {
                $lifecycle->syncAssistants($data['assistants']);
            }

            // 同步标签
            if (! empty($data['tag_ids'])) {
                $lifecycle->syncTags($data['tag_ids']);
            }

            // 处理附件
            if (! empty($data['attachments'])) {
                $this->syncAttachments($lifecycle, $data['attachments']);
            }

            return $lifecycle->load([
                'assets:id,name,brand_id,model,serial_number',
                'initiator:id,nickname',
                'assistants:id,nickname',
                'acceptanceEntity:id,name',
                'acceptancePersonnel:id,name',
                'attachments',
                'tags:id,name,category',
            ]);
        });
    }

    /**
     * 更新生命周期
     */
    public function update(Lifecycle $lifecycle, array $data): Lifecycle
    {
        return DB::transaction(function () use ($lifecycle, $data) {
            // 验证验收人员是否属于验收相关方
            if (isset($data['acceptance_entity_id']) || isset($data['acceptance_personnel_id'])) {
                $entityId = $data['acceptance_entity_id'] ?? $lifecycle->acceptance_entity_id;
                $personnelId = $data['acceptance_personnel_id'] ?? $lifecycle->acceptance_personnel_id;
                $this->validateAcceptancePersonnel($entityId, $personnelId);
            }

            // 更新生命周期
            $lifecycle->update([
                'type' => $data['type'] ?? $lifecycle->type,
                'date' => $data['date'] ?? $lifecycle->date,
                'initiator_id' => $data['initiator_id'] ?? $lifecycle->initiator_id,
                'content' => $data['content'] ?? $lifecycle->content,
                'acceptance_entity_id' => $data['acceptance_entity_id'] ?? $lifecycle->acceptance_entity_id,
                'acceptance_personnel_id' => $data['acceptance_personnel_id'] ?? $lifecycle->acceptance_personnel_id,
                'acceptance_time' => $data['acceptance_time'] ?? $lifecycle->acceptance_time,
                'updated_by' => auth()->id(),
                'is_checked' => $data['is_checked'] ?? $lifecycle->is_checked,
            ]);

            // 同步资产
            if (isset($data['assets'])) {
                $lifecycle->syncAssets($data['assets']);
            }

            // 同步协助人员
            if (isset($data['assistants'])) {
                $lifecycle->syncAssistants($data['assistants']);
            }

            // 同步标签
            if (isset($data['tag_ids'])) {
                $lifecycle->syncTags($data['tag_ids']);
            }

            // 处理附件
            if (isset($data['attachments'])) {
                $this->syncAttachments($lifecycle, $data['attachments']);
            }

            return $lifecycle->load([
                'assets:id,name,brand_id,model,serial_number',
                'initiator:id,nickname',
                'assistants:id,nickname',
                'acceptanceEntity:id,name',
                'acceptancePersonnel:id,name',
                'attachments',
                'tags:id,name,category',
            ]);
        });
    }

    /**
     * 删除生命周期
     */
    public function delete(Lifecycle $lifecycle): bool
    {
        return $lifecycle->delete();
    }

    /**
     * 获取验收人员列表
     */
    public function getAcceptancePersonnel(int $entityId): array
    {
        return EntityContact::where('entity_id', $entityId)
            ->select(['id', 'name', 'phone', 'position', 'department'])
            ->get()
            ->toArray();
    }

    /**
     * 验证验收人员是否属于验收相关方
     */
    private function validateAcceptancePersonnel(int $entityId, int $personnelId): void
    {
        $exists = EntityContact::where('entity_id', $entityId)
            ->where('id', $personnelId)
            ->exists();

        if (! $exists) {
            throw new \InvalidArgumentException('验收人员不属于所选验收相关方');
        }
    }

    /**
     * 同步附件
     */
    private function syncAttachments(Lifecycle $lifecycle, array $attachmentIds): void
    {
        $attachmentData = [];
        foreach ($attachmentIds as $index => $id) {
            $attachmentData[] = [
                'id' => $id,
                'category' => AttachmentCategory::GENERAL->value,
                'sort' => $index,
                'description' => null,
            ];
        }

        $lifecycle->syncAttachments($attachmentData);
    }

    /**
     * 判断标签完成情况
     */
    public function isTagsCompleted(Lifecycle $lifecycle): bool
    {
        $tagIds = $lifecycle->followUps()->pluck('tag_ids')->filter()->flatMap(function ($ids) {
            return is_array($ids) ? $ids : json_decode($ids, true) ?? [];
        })->unique()->values()->all();

        return count($tagIds) === count($lifecycle->tags);
    }
}
