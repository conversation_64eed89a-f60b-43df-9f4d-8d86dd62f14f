<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建生命周期管理表
        Schema::create('lifecycles', function (Blueprint $table) {
            $table->id();
            $table->string('type', 50)->comment('类型');
            $table->unsignedInteger('date')->nullable()->comment('日期');
            $table->unsignedBigInteger('initiator_id')->nullable()->comment('发起人ID');
            $table->text('content')->nullable()->comment('内容');
            $table->unsignedBigInteger('acceptance_entity_id')->nullable()->comment('验收相关方ID');
            $table->unsignedBigInteger('acceptance_personnel_id')->nullable()->comment('验收人员ID');
            $table->unsignedInteger('acceptance_time')->nullable()->comment('验收时间');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->unsignedInteger('is_checked')->nullable()->comment('是否已验收');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
            // $table->softDeletes();

            // 索引
            $table->index('type');
            $table->index('date');
            $table->index('initiator_id');
            $table->index('acceptance_entity_id');
            $table->index('acceptance_personnel_id');
            $table->index('created_at');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            $table->comment('生命周期管理表');
        });

        // 创建生命周期协助人员关联表
        Schema::create('lifecycle_assistants', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引
            $table->unique(['lifecycle_id', 'user_id']);
            $table->index('lifecycle_id');
            $table->index('user_id');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            $table->comment('生命周期协助人员关联表');
        });

        // 创建生命周期跟进记录表
        Schema::create('lifecycle_follow_ups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedInteger('date')->comment('日期');
            $table->unsignedBigInteger('person_id')->comment('跟进人ID');
            $table->json('tag_ids')->nullable()->comment('标签ID数组');
            $table->text('content')->comment('内容');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引
            $table->index('lifecycle_id');
            $table->index('date');
            $table->index('person_id');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            $table->comment('生命周期跟进记录表');
        });

        // 创建生命周期与资产关联表
        Schema::create('lifecycle_assets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedBigInteger('asset_id')->comment('资产ID');
            $table->text('notes')->nullable()->comment('备注说明');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            $table->unique(['lifecycle_id', 'asset_id']);
            $table->index('lifecycle_id');
            $table->index('asset_id');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            $table->comment('生命周期与资产关联表');
        });

        // 创建生命周期与标签关联表
        Schema::create('lifecycle_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedBigInteger('tag_id')->comment('标签ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            $table->unique(['lifecycle_id', 'tag_id']);
            $table->index('lifecycle_id');
            $table->index('tag_id');

            // 外键约束已移除，使用 Laravel ORM 关系管理

            $table->comment('生命周期与标签关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lifecycle_tags');
        Schema::dropIfExists('lifecycle_assets');
        Schema::dropIfExists('lifecycle_follow_ups');
        Schema::dropIfExists('lifecycle_assistants');
        Schema::dropIfExists('lifecycles');
    }
};
