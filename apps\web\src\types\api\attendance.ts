// 考勤管理相关类型定义

// 考勤配置实体
export interface CheckinConfig {
  id: number
  lifecycle_id: number // 关联的生命周期项目ID
  lifecycle_name?: string // 生命周期项目名称（显示用）
  location_address: string // 打卡地点地址
  location_latitude: number // 打卡地点纬度
  location_longitude: number // 打卡地点经度
  location_radius: number // 允许打卡范围（米）
  checkin_time: string // 打卡时间（HH:mm）
  participant_ids: number[] // 参与人员ID数组
  participants?: CheckinParticipant[] // 参与人员详情（显示用）
  require_photo: boolean // 是否需要拍照打卡
  status: 'active' | 'inactive' // 配置状态
  created_at: string
  updated_at: string
}


// 参与人员
export interface CheckinParticipant {
  id: number
  name: string
  avatar?: string
}

// 打卡记录实体
export interface CheckinRecord {
  id: number
  config_id: number // 配置ID
  config_name?: string // 配置名称（显示用）
  user_id: number
  user_name: string
  user_avatar?: string
  checkin_time: number // 打卡时间戳
  checkin_date: string // 打卡日期（YYYY-MM-DD）
  location_address: string // 打卡地点
  location_latitude: number // 打卡纬度
  location_longitude: number // 打卡经度
  status: 0 | 2 // 0-正常 2-异常
  status_label: string // 状态标签
  distance?: number // 距离标准地点的距离（米）
  attachments?: number[] // 附件ID数组（异常说明图片等）
  created_at: string
}

// 考勤统计数据
export interface CheckinStatistics {
  total_configs: number // 总配置数
  active_configs: number // 活跃配置数
  total_participants: number // 总参与人数
  today_checkin_count: number // 今日打卡次数
  normal_rate: number // 正常率（百分比）
  exception_count: number // 异常次数
  recent_records: CheckinRecord[] // 最近打卡记录
}

// 考勤配置表单数据
export interface CheckinConfigForm {
  lifecycle_id: number
  location_address: string
  location_latitude: number
  location_longitude: number
  location_radius: number
  checkin_time: string
  participant_ids: number[]
  require_photo: boolean
  status: 'active' | 'inactive'
}

// 考勤配置搜索参数
export interface CheckinConfigSearchParams {
  lifecycle_id?: number // 生命周期项目ID
  status?: 'active' | 'inactive' // 状态
  page?: number
  per_page?: number
}

// 打卡记录搜索参数
export interface CheckinRecordSearchParams {
  config_id?: number // 配置ID
  user_id?: number // 用户ID
  status?: 0 | 2 // 打卡状态：0-正常 2-异常
  start_date?: string // 开始日期
  end_date?: string // 结束日期
  page?: number
  per_page?: number
}

// 生命周期选项
export interface LifecycleOption {
  id: number
  name: string
}

// 用户选项
export interface UserOption {
  id: number
  name: string
  avatar?: string
}
