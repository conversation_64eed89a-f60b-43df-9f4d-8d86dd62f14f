<template>
  <div class="asset-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchForm"
      :items="searchFormItems"
      :showExpand="true"
      @search="() => handleSearch()"
      @reset="handleReset"
    ></ArtSearchBar>

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton type="primary" @click="handleAdd" v-ripple v-if="hasAuth('add')">
            <ElIcon><Plus /></ElIcon>
            新增资产
          </ElButton>

          <!-- 批量操作按钮组 -->
          <ElButton type="warning" :disabled="!hasSelectedRows" @click="handleBatchCopy" v-ripple>
            <ElIcon><CopyDocument /></ElIcon>
            批量复制{{ hasSelectedRows ? `(${selectedCount})` : '' }}
          </ElButton>

          <ElButton type="danger" :disabled="!hasSelectedRows" @click="handleBatchDelete" v-ripple>
            <ElIcon><Delete /></ElIcon>
            批量删除{{ hasSelectedRows ? `(${selectedCount})` : '' }}
          </ElButton>

          <ElButton
            type="success"
            :disabled="!hasSelectedRows"
            @click="handleBatchAddLifecycle"
            v-ripple
          >
            <ElIcon><Calendar /></ElIcon>
            批量新增生命周期计划{{ hasSelectedRows ? `(${selectedCount})` : '' }}
          </ElButton>

          <ElButton type="info" @click="handleBatchImport" v-ripple>
            <ElIcon><Upload /></ElIcon>
            批量导入
          </ElButton>

          <ElButton type="info" @click="handleBatchExport" v-ripple>
            <ElIcon><Download /></ElIcon>
            批量导出
          </ElButton>

          <ElButton
            type="primary"
            :disabled="!hasSelectedRows"
            @click="handleBatchPrintQrcode"
            v-ripple
          >
            <ElIcon><Printer /></ElIcon>
            批量打印二维码{{ hasSelectedRows ? `(${selectedCount})` : '' }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        :loading="loading"
        :data="tableData"
        :columns="visibleColumns"
        :pagination="paginationConfig"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @row:selection-change="handleSelectionChange"
      />

      <!-- 新增/编辑抽屉 -->
      <AssetFormDrawer
        v-model="drawerVisible"
        :asset-id="currentAssetId"
        @success="handleDrawerSuccess"
      />

      <!-- 资产详情弹窗 -->
      <AssetDetailDialog v-model:visible="detailDialogVisible" :asset-id="currentDetailAssetId" />

      <!-- 批量打印二维码弹窗 -->
      <QrcodeBatchPrint v-model:visible="batchPrintVisible" :assets="selectedRows" />

      <!-- 批量导入弹窗 -->
      <BatchImportDialog
        v-model:visible="importDialogVisible"
        entity-name="资产"
        :export-template-api="exportAssetTemplate"
        :import-api="importAssets"
        :get-task-status-api="getImportTaskStatus"
        @import-success="handleImportSuccess"
      />

      <!-- 批量新增生命周期对话框 -->
      <LifecycleDialog
        v-model="batchLifecycleDialogVisible"
        type="add"
        :batch-mode="true"
        :preselected-assets="batchSelectedAssets"
        @success="handleBatchLifecycleSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AssetManagement' })

  // Vue 核心
  import { ref, reactive, computed, onMounted, h } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox, ElImage, ElButton, ElIcon } from 'element-plus'
  import {
    CopyDocument,
    Delete,
    Calendar,
    Upload,
    Download,
    Plus,
    Printer
  } from '@element-plus/icons-vue'

  // 内部 hooks
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import AssetFormDrawer from './components/AssetFormDrawer.vue'
  import AssetDetailDialog from './components/AssetDetailDialog.vue'
  import QrcodeBatchPrint from './components/QrcodeBatchPrint.vue'
  import { BatchImportDialog } from '@/components/custom/batch-import-dialog'
  import LifecycleDialog from '../lifecycle/components/LifecycleDialog.vue'

  // API
  import {
    getAssetList,
    deleteAsset,
    batchCopyAssets,
    batchDeleteAssets,
    exportAssetTemplate,
    importAssets,
    getImportTaskStatus
  } from '@/api/admin/asset'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { useRegionStore } from '@/store/modules/region'
  import { formatDate } from '@/utils'

  // 类型定义
  import type { ColumnOption } from '@/types/component'
  import type { Asset, AssetQueryParams } from '@/types/api/asset'
  import type { DictionaryItem } from '@/types/api/dictionary'

  // 搜索表单
  const searchForm = reactive({
    keyword: '',
    asset_source: null,
    asset_status: null,
    asset_condition: null,
    is_accessory: null as string | null,
    asset_category_id: undefined as number | undefined,
    department_category_id: undefined as number | undefined,
    industry_category_id: undefined as number | undefined
  })

  // 搜索表单配置
  const searchFormItems = computed(() => [
    {
      prop: 'keyword',
      label: '关键词',
      type: 'input' as const,
      placeholder: '资产名称/品牌/型号/序列号'
    },
    {
      prop: 'asset_source',
      label: '资产来源',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择资产来源'
      },
      options: () =>
        assetSourceOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'asset_status',
      label: '资产状态',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择资产状态'
      },
      options: () =>
        assetStatusOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'asset_condition',
      label: '成色',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择成色'
      },
      options: () =>
        assetConditionOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'is_accessory',
      label: '是否附属设备',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择'
      },
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' }
      ]
    }
  ])

  // 分页
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20,
    total: 0
  })

  // 表格数据
  const loading = ref(false)
  const tableData = ref<Asset[]>([])

  // 地区名称映射
  const regionNameMap = ref<Record<string, string>>({})

  // 抽屉
  const drawerVisible = ref(false)
  const currentAssetId = ref<number | undefined>(undefined)
  const tableRef = ref()

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailAssetId = ref<number | undefined>(undefined)

  // 批量打印二维码弹窗
  const batchPrintVisible = ref(false)

  // 批量导入弹窗
  const importDialogVisible = ref(false)

  // 批量生命周期对话框状态
  const batchLifecycleDialogVisible = ref(false)
  const batchSelectedAssets = ref<Asset[]>([])

  // 选中行数据
  const selectedRows = ref<Asset[]>([])

  // 选中行状态
  const hasSelectedRows = computed(() => selectedRows.value.length > 0)
  const selectedCount = computed(() => selectedRows.value.length)

  // Store
  const dictionaryStore = useDictionaryStore()
  const regionStore = useRegionStore()

  // 权限控制
  const { hasAuth } = useAuth()

  // 字典选项
  const assetSourceOptions = ref<DictionaryItem[]>([])
  const assetStatusOptions = ref<DictionaryItem[]>([])
  const assetConditionOptions = ref<DictionaryItem[]>([])

  /**
   * 判断是否为图片文件
   */
  const isImageFile = (attachment: any) => {
    // 检查 MIME 类型
    if (attachment.mime_type) {
      return attachment.mime_type.startsWith('image/')
    }

    // 如果没有 MIME 类型，通过文件扩展名判断
    const fileName = attachment.file_name || ''
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
    return imageExtensions.some((ext) => fileName.toLowerCase().endsWith(ext))
  }

  /**
   * 获取资产的主图
   */
  const getAssetMainImage = (asset: Asset) => {
    if (!asset.attachments || asset.attachments.length === 0) {
      return null
    }

    // 查找第一个图片附件
    const firstImage = asset.attachments.find((attachment) => isImageFile(attachment))
    return firstImage || null
  }

  /**
   * 获取资产的所有图片附件
   */
  const getAssetImages = (asset: Asset) => {
    if (!asset.attachments || asset.attachments.length === 0) {
      return []
    }

    return asset.attachments
      .filter((attachment) => isImageFile(attachment))
      .map((attachment) => attachment.file_url)
      .filter((url): url is string => Boolean(url))
  }

  // 定义表格列
  const columns = ref<ColumnOption[]>([
    {
      type: 'selection',
      width: 50,
      show: true
    },
    {
      prop: 'main_image',
      label: '主图',
      width: 80,
      show: true,
      align: 'center',
      formatter: (row: Asset) => {
        const mainImage = getAssetMainImage(row)
        const allImages = getAssetImages(row)

        if (!mainImage || !mainImage.file_url) {
          // 没有图片时显示占位符
          return h(
            'div',
            {
              class: 'main-image-placeholder',
              style: {
                width: '40px',
                height: '40px',
                backgroundColor: '#f5f7fa',
                border: '1px dashed #dcdfe6',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#c0c4cc',
                fontSize: '12px'
              }
            },
            '无图片'
          )
        }

        return h(ElImage, {
          src: mainImage.file_url,
          fit: 'cover',
          class: 'main-image',
          style: {
            width: '40px',
            height: '40px',
            borderRadius: '4px',
            cursor: 'pointer'
          },
          previewSrcList: allImages.length > 0 ? allImages : [mainImage.file_url],
          previewTeleported: true,
          loading: 'lazy'
        })
      }
    },
    {
      prop: 'name',
      label: '资产名称',
      minWidth: 180,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'brand',
      label: '品牌',
      width: 180,
      show: true,
      formatter: (row: Asset) => row.brand?.name || '-'
    },
    {
      prop: 'model',
      label: '规格型号',
      width: 150,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'serial_number',
      label: '序列号',
      width: 150,
      checked: false,
      showOverflowTooltip: true
    },
    {
      prop: 'qrcode',
      label: '二维码',
      width: 80,
      show: true,
      align: 'center',
      formatter: (row: Asset) => {
        return h(
          ElButton,
          {
            size: 'small',
            type: 'primary',
            onClick: (e: Event) => {
              e.stopPropagation()
              handleSingleQrcodePrint(row)
            }
          },
          () => '打印'
        )
      }
    },
    {
      prop: 'asset_source',
      label: '资产来源',
      width: 100,
      show: true,
      formatter: (row: Asset) => {
        const item = assetSourceOptions.value.find((opt) => opt.code === row.asset_source)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_source || '-'
      }
    },
    {
      prop: 'asset_status',
      label: '资产状态',
      width: 120,
      show: true,
      formatter: (row: Asset) => {
        const item = assetStatusOptions.value.find((opt) => opt.code === row.asset_status)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_status || '-'
      }
    },
    {
      prop: 'asset_condition',
      label: '成色',
      width: 80,
      show: true,
      formatter: (row: Asset) => {
        const item = assetConditionOptions.value.find((opt) => opt.code === row.asset_condition)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_condition || '-'
      }
    },
    {
      prop: 'is_accessory',
      label: '附属设备',
      width: 100,
      show: true,
      formatter: (row: Asset) => (row.parent_id ? '是' : '否')
    },
    {
      prop: 'parent',
      label: '主设备',
      width: 150,
      checked: false,
      formatter: (row: Asset) => row.parent?.name || '-',
      showOverflowTooltip: true
    },
    {
      prop: 'location',
      label: '所在地',
      minWidth: 200,
      show: true,
      formatter: (row: Asset) => {
        if (!row.region_code) return '-'
        return regionNameMap.value[row.region_code] || row.region_code
      },
      showOverflowTooltip: true
    },
    {
      prop: 'start_date',
      label: '启用日期',
      width: 110,
      show: true,
      formatter: (row: Asset) => (row.start_date ? formatDate(row.start_date, 'YYYY-MM-DD') : '-')
    },
    {
      prop: 'attachments_count',
      label: '附件数量',
      width: 100,
      checked: false,
      formatter: (row: Asset) => row.attachments_count || 0
    },
    {
      prop: 'children_count',
      label: '附属设备',
      width: 100,
      checked: false,
      formatter: (row: Asset) => row.children_count || 0
    },
    {
      prop: 'created_at',
      label: '创建时间',
      width: 160,
      checked: false,
      sortable: true,
      formatter: (row: Asset) => formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
    {
      prop: 'operation',
      label: '操作',
      width: 180,
      fixed: 'right' as const,
      show: true,
      formatter: (row: Asset) => {
        const buttons = [
          h(ArtButtonTable, {
            type: 'view',
            onClick: () => handleDetail(row)
          })
        ]

        if (hasAuth('edit')) {
          buttons.push(
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleEdit(row)
            })
          )
        }

        if (hasAuth('delete')) {
          buttons.push(
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
          )
        }

        return h('div', { style: 'display: flex; gap: 5px;' }, buttons)
      }
    }
  ])

  // 使用列选择组合式函数
  const { columns: visibleColumns, columnChecks } = useCheckedColumns(() => columns.value)

  // 分页配置
  const paginationConfig = computed(() => ({
    current: pagination.currentPage,
    size: pagination.pageSize,
    total: pagination.total
  }))

  // 表格配置
  const tableConfig = {
    rowKey: 'id',
    height: undefined,
    maxHeight: '600px'
  }

  // 加载字典数据
  const loadDictionaries = async () => {
    const [sourceData, statusData, conditionData] = await Promise.all([
      dictionaryStore.fetchItemsByCode('asset_source'),
      dictionaryStore.fetchItemsByCode('asset_status'),
      dictionaryStore.fetchItemsByCode('asset_condition')
    ])

    assetSourceOptions.value = sourceData || []
    assetStatusOptions.value = statusData || []
    assetConditionOptions.value = conditionData || []
  }

  // 加载地区名称
  const loadRegionNames = async (assets: Asset[]) => {
    const regionCodes = [
      ...new Set(
        assets.map((asset) => asset.region_code).filter((code): code is string => Boolean(code))
      )
    ]

    if (regionCodes.length === 0) return

    // 批量获取地区名称
    await Promise.all(
      regionCodes.map(async (code) => {
        if (code && !regionNameMap.value[code]) {
          const name = await regionStore.getRegionNameByCode(code)
          regionNameMap.value[code] = name
        }
      })
    )
  }

  // 获取列表数据
  const getList = async () => {
    loading.value = true
    try {
      const params: AssetQueryParams = {
        keyword: searchForm.keyword || undefined,
        asset_source: searchForm.asset_source || undefined,
        asset_status: searchForm.asset_status || undefined,
        asset_condition: searchForm.asset_condition || undefined,
        is_accessory:
          searchForm.is_accessory === 'true'
            ? true
            : searchForm.is_accessory === 'false'
              ? false
              : undefined,
        asset_category_ids: searchForm.asset_category_id
          ? [searchForm.asset_category_id]
          : undefined,
        // department_category_id: searchForm.department_category_id,
        // industry_category_id: searchForm.industry_category_id,
        page: pagination.currentPage,
        per_page: pagination.pageSize
      }

      const res = await getAssetList(params)

      tableData.value = res.data || []
      pagination.total = res.meta.total || 0

      // 加载地区名称
      await loadRegionNames(tableData.value)
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    pagination.currentPage = 1
    getList()
  }

  // 重置
  const handleReset = () => {
    searchForm.keyword = ''
    searchForm.asset_source = null
    searchForm.asset_status = null
    searchForm.asset_condition = null
    searchForm.is_accessory = null
    searchForm.asset_category_id = undefined
    searchForm.department_category_id = undefined
    searchForm.industry_category_id = undefined
    handleSearch()
  }

  // 刷新
  const handleRefresh = () => {
    getList()
  }

  // 分页大小变化
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.currentPage = 1
    getList()
  }

  // 当前页变化
  const handleCurrentChange = (current: number) => {
    pagination.currentPage = current
    getList()
  }

  // 新增
  const handleAdd = () => {
    currentAssetId.value = undefined
    drawerVisible.value = true
  }

  // 详情
  const handleDetail = (row: Asset) => {
    currentDetailAssetId.value = row.id
    detailDialogVisible.value = true
  }

  // 编辑
  const handleEdit = (row: Asset) => {
    currentAssetId.value = row.id
    drawerVisible.value = true
  }

  // 删除
  const handleDelete = async (row: Asset) => {
    try {
      await ElMessageBox.confirm(`确定要删除资产"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteAsset(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 抽屉成功
  const handleDrawerSuccess = () => {
    getList()
  }

  // 选中行变化事件
  const handleSelectionChange = (selection: Asset[]) => {
    selectedRows.value = selection
  }

  // 批量复制
  const handleBatchCopy = async () => {
    if (!hasSelectedRows.value) {
      ElMessage.warning('请先选择要复制的资产')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要复制选中的 ${selectedCount.value} 条资产吗？`,
        '批量复制确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      const ids = selectedRows.value.map((row) => row.id)
      await batchCopyAssets(ids)

      ElMessage.success(`成功复制 ${selectedCount.value} 条资产`)

      // 清空选择并刷新列表
      tableRef.value?.clearSelection()
      await getList()
    } catch {
      // 用户取消或API错误，错误已由拦截器处理
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (!hasSelectedRows.value) {
      ElMessage.warning('请先选择要删除的资产')
      return
    }

    try {
      await ElMessageBox.confirm(
        `此操作将永久删除选中的 ${selectedCount.value} 条资产，是否继续？`,
        '批量删除警告',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const ids = selectedRows.value.map((row) => row.id)
      await batchDeleteAssets(ids)

      ElMessage.success(`成功删除 ${selectedCount.value} 条资产`)

      // 清空选择并刷新列表
      tableRef.value?.clearSelection()
      await getList()
    } catch {
      // 用户取消或API错误，错误已由拦截器处理
    }
  }

  // 批量新增生命周期计划
  const handleBatchAddLifecycle = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请先选择要添加生命周期计划的资产')
      return
    }

    // 复制选中的资产，避免引用问题
    batchSelectedAssets.value = [...selectedRows.value]
    batchLifecycleDialogVisible.value = true
  }

  // 批量导入
  const handleBatchImport = () => {
    importDialogVisible.value = true
  }

  // 批量导出 (下载模板)
  const handleBatchExport = async () => {
    try {
      const blob = await exportAssetTemplate()

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `资产导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败，请重试')
    }
  }

  // 导入成功处理
  const handleImportSuccess = () => {
    getList() // 刷新列表
  }

  // 批量生命周期对话框成功回调
  const handleBatchLifecycleSuccess = () => {
    // 刷新表格数据
    handleRefresh()
    // 清空选中状态
    tableRef.value?.clearSelection()
    // 重置批量状态
    batchSelectedAssets.value = []
  }

  // 批量打印二维码
  const handleBatchPrintQrcode = () => {
    if (!hasSelectedRows.value) {
      ElMessage.warning('请先选择要打印二维码的资产')
      return
    }

    batchPrintVisible.value = true
  }

  // 单个二维码打印
  const handleSingleQrcodePrint = (asset: Asset) => {
    // 设置选中的资产为当前点击的资产
    selectedRows.value = [asset]
    batchPrintVisible.value = true
  }

  // 初始化
  onMounted(() => {
    loadDictionaries()
    getList()
  })
</script>

<style lang="scss" scoped>
  .asset-page {
    height: 100%;
    padding: 20px;

    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  // 批量操作按钮组样式
  :deep(.art-table-header) {
    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 主图列样式
  :deep(.el-table) {
    .main-image {
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: scale(1.05);
      }
    }

    .main-image-placeholder {
      transition: all 0.3s ease;

      &:hover {
        background-color: #fafafa;
        border-color: #c0c4cc;
      }
    }
  }
</style>
