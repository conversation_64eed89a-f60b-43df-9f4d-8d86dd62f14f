<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CheckinConfigRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'attachable_type' => 'required|string|max:50',
            'attachable_id' => 'required|integer|min:1',
            'location' => 'nullable|string|max:255',
            'longitude' => 'nullable|min:1',
            'latitude' => 'nullable|min:1',
            'checkin_time' => 'required|integer',
            'location_range' => 'nullable|integer|min:1',
            'status' => 'integer|in:0,1',
            'is_photo' => 'integer|in:0,1',
            'user_ids' => 'required|array',
            'user_ids.*' => 'integer|exists:users,id'
        ];

        if ($this->isMethod('PUT')) {
            $rules = array_map(function ($rule) {
                return str_replace('required|', '', $rule);
            }, $rules);
        }

        return $rules;
    }

    public function attributes()
    {
        return [
            'attachable_type' => '所属模块',
            'attachable_id' => '关联业务ID',
            'checkin_time' => '打卡时间',
            'status' => '状态',
            'is_photo' => '是否拍照',
            'location' => '打卡地点',
            'longitude' => '打卡位置经度',
            'latitude' => '打卡位置纬度',
            'location_range' => '打卡范围',
            'user_ids' => '打卡人员',
            'user_ids.*' => '打卡人员ID'
        ];
    }

    public function messages()
    {
        return [
            'attachable_type.required' => '所属模块不能为空',
            'attachable_id.required' => '关联业务ID不能为空',
            'attachable_id.integer' => '关联业务ID必须是整数',
            'attachable_id.min' => '关联业务ID必须大于0',
            'checkin_time.required' => '打卡时间不能为空',
            'checkin_time.integer' => '打卡时间必须是整数',
            'status.integer' => '状态必须是整数',
            'status.in' => '状态必须是0或1',
            'is_photo.integer' => '是否拍照必须是整数',
            'is_photo.in' => '是否拍照必须是0或1',
            'location.string' => '打卡地点必须是字符串',
            'location.max' => '打卡地点不能超过255个字符',
            'location_range.integer' => '打卡范围必须是整数',
            'location_range.min' => '打卡范围必须大于0',
            'user_ids.required' => '打卡人员不能为空',
            'user_ids.*.integer' => '打卡人员ID必须是整数',
            'user_ids.*.exists' => '打卡人员ID不存在',
        ];
    }
}
