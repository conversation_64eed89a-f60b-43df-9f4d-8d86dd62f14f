import request from '@/utils/http'
import type { AttachmentItem, AttachmentSearchParams } from '@/types/api'
import type { PaginatedResponse } from '@/types/api/pagination'

// ========== 附件管理 ==========

/**
 * 获取附件列表
 */
export const getAttachmentList = (
  params: AttachmentSearchParams = {}
): Promise<PaginatedResponse<AttachmentItem>> => {
  return request.get<PaginatedResponse<AttachmentItem>>({
    url: '/api/admin/attachments',
    params: {
      ...params,
      per_page: params.size || params.per_page || 20,
      page: params.current || params.page || 1
    }
  })
}

/**
 * 获取附件详情
 */
export const getAttachmentDetail = (id: string): Promise<AttachmentItem> => {
  return request.get<AttachmentItem>({
    url: `/api/admin/attachments/${id}`
  })
}

/**
 * 更新附件描述
 */
export const updateAttachmentDescription = (
  id: string,
  description: string
): Promise<AttachmentItem> => {
  return request.put<AttachmentItem>({
    url: `/api/admin/attachments/${id}/description`,
    data: { description }
  })
}

// ========== 上传相关接口 ==========

/**
 * OSS临时凭证类型
 */
export interface STSCredentials {
  access_key_id: string
  access_key_secret: string
  security_token: string
  expiration: string
}

/**
 * STS凭证请求参数
 */
export interface STSCredentialsParams {
  filename: string
  filesize: number
  mime_type: string
  md5_hash?: string
}

/**
 * STS凭证响应（包含上传配置或秒传结果）
 */
export interface STSResponse {
  // STS凭证相关（正常上传时返回）
  credentials?: STSCredentials
  region?: string
  bucket?: string
  endpoint?: string
  prefix?: string
  upload_id?: string

  // 秒传时直接返回附件信息
  id?: number
  file_name?: string
  file_url?: string
  file_size?: number
  mime_type?: string
}

/**
 * 获取STS临时凭证
 */
export const getSTSCredentials = (params: STSCredentialsParams): Promise<STSResponse> => {
  return request.post<STSResponse>({
    url: '/api/admin/attachments/sts-credentials',
    data: params
  })
}

/**
 * 上传附件（本地存储）
 */
export const uploadAttachment = (file: File, category?: string): Promise<AttachmentItem> => {
  const formData = new FormData()
  formData.append('file', file)
  if (category) {
    formData.append('category', category)
  }

  return request.post<AttachmentItem>({
    url: '/api/admin/attachments/upload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 确认上传参数
 */
export interface ConfirmUploadParams {
  upload_id: string
  object_key: string
  filename: string
  filesize: number
  mime_type: string
  category?: string
}

/**
 * 确认上传（OSS上传后调用）
 */
export const confirmUpload = (data: ConfirmUploadParams): Promise<AttachmentItem> => {
  return request.post<AttachmentItem>({
    url: '/api/admin/attachments/confirm-upload',
    data
  })
}
