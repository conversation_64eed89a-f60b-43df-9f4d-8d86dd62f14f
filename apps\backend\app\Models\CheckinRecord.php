<?php

namespace App\Models;

use App\Models\BaseModel;

class CheckinRecord extends BaseModel
{
    protected $fillable = [
        'checkin_config_id',
        'user_id',
        'checkin_time',
        'status',
        'location_range',
        'location',
        'latitude',
        'longitude',
        'ip_address',
        'attachment_id',
        'content'
    ];

    protected $casts = [
        'checkin_config_id' => 'integer',
        'user_id' => 'integer',
        'checkin_time' => 'integer',
        'status' => 'integer',
        'location_range' => 'integer',
        'attachment_id' => 'integer'
    ];

    public function config()
    {
        return $this->belongsTo(CheckinConfig::class, 'checkin_config_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function attachment()
    {
        return $this->belongsTo(Attachment::class);
    }
}
