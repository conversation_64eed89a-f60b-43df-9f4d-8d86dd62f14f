<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

/**
 * @property int $id
 * @property int|null $asset_id 资产ID
 * @property string $type 类型
 * @property int|null $date 日期
 * @property int|null $initiator_id 发起人ID
 * @property string|null $content 内容
 * @property int|null $acceptance_entity_id 验收相关方ID
 * @property int|null $acceptance_personnel_id 验收人员ID
 * @property int|null $acceptance_time 验收时间
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $is_checked 是否已验收
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \App\Models\Entity|null $acceptanceEntity
 * @property-read \App\Models\EntityContact|null $acceptancePersonnel
 * @property-read \App\Models\Asset|null $asset
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $assistants
 * @property-read int|null $assistants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LifecycleFollowUp> $followUps
 * @property-read int|null $follow_ups_count
 * @property-read \Illuminate\Support\Collection $follow_ups_tags
 * @property-read float $progress
 * @property-read \App\Models\User|null $initiator
 * @property-read \App\Models\User|null $updater
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Tag> $tags
 * @property-read int|null $tags_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptancePersonnelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAssetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereInitiatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Lifecycle extends BaseModel
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'type',
        'date',
        'initiator_id',
        'content',
        'acceptance_entity_id',
        'acceptance_personnel_id',
        'acceptance_time',
        'created_by',
        'updated_by',
        'is_checked',
    ];

    protected $casts = [
        'date' => 'integer',
        'acceptance_time' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'is_checked' => 'integer',
    ];

    protected $appends = ['follow_ups_tags', 'progress'];

    /**
     * 资产（多对多）
     */
    public function assets(): BelongsToMany
    {
        return $this->belongsToMany(Asset::class, 'lifecycle_assets', 'lifecycle_id', 'asset_id')
            ->withPivot('notes');
    }

    /**
     * 发起人
     */
    public function initiator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiator_id');
    }

    /**
     * 协助人员
     */
    public function assistants(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'lifecycle_assistants', 'lifecycle_id', 'user_id');
    }

    /**
     * 验收相关方
     */
    public function acceptanceEntity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'acceptance_entity_id');
    }

    /**
     * 验收人员
     */
    public function acceptancePersonnel(): BelongsTo
    {
        return $this->belongsTo(EntityContact::class, 'acceptance_personnel_id');
    }

    /**
     * 跟进记录
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(LifecycleFollowUp::class);
    }

    /**
     * 获取生命周期跟进记录所有标签ID数组
     *
     * @return \Illuminate\Support\Collection
     */
    public function getFollowUpsTagsAttribute()
    {
        $tagIds = $this->followUps()->pluck('tag_ids')->filter()->flatMap(function ($ids) {
            return is_array($ids) ? $ids : json_decode($ids, true) ?? [];
        })->unique()->values()->all();

        // return Tag::whereIn('id', $tagIds)->get();
        // return $this->followUps()->pluck('tag_ids');
        return $tagIds;
    }

    /**
     * 完成进度，对标签对比
     */
    public function getProgressAttribute()
    {
        $tagIds = $this->followUps()->pluck('tag_ids')->filter()->flatMap(function ($ids) {
            return is_array($ids) ? $ids : json_decode($ids, true) ?? [];
        })->unique()->values()->all();

        if ($this->is_checked === 1) {
            return 100;
        }

        // 如果没有关联标签，返回0
        if ($this->tags->count() === 0) {
            return 0;
        }

        $progress = count($tagIds) / $this->tags->count();

        return $progress * 100;
    }

    /**
     * 创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 是否是协助人员
     */
    public function isAssistant(int $userId): bool
    {
        return $this->assistants()->where('user_id', $userId)->exists();
    }

    /**
     * 标签
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'lifecycle_tags', 'lifecycle_id', 'tag_id');
    }

    /**
     * 同步资产
     */
    public function syncAssets(array $assetIds): void
    {
        $pivotData = [];
        foreach ($assetIds as $assetId) {

            $pivotData[$assetId] = ['created_at' => now()->timestamp, 'updated_at' => now()->timestamp];
        }

        $this->assets()->sync($pivotData);
    }

    /**
     * 同步标签
     */
    public function syncTags(array $tagIds): void
    {
        $this->tags()->sync($tagIds, ['created_at' => now()->timestamp, 'updated_at' => now()->timestamp]);
    }

    /**
     * 同步协助人员
     */
    public function syncAssistants(array $userIds): void
    {
        $this->assistants()->sync($userIds, ['created_at' => now()->timestamp, 'updated_at' => now()->timestamp]);
    }

    /**
     * 验收
     */
    public function accept(array $data): void
    {
        $this->where('id', $this->id)->update([
            'is_checked' => $data['is_checked'],
            'acceptance_personnel_id' => Auth::user()->id,
            'acceptance_time' => now()->timestamp,
        ]);
    }
}
