name: 操作日志管理
description: |-

  系统操作日志查询接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/operation-logs
    metadata:
      groupName: 操作日志管理
      groupDescription: |-

        系统操作日志查询接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取操作日志列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      user_name:
        name: user_name
        description: 用户名搜索
        required: false
        example: admin
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      ip:
        name: ip
        description: IP地址搜索
        required: false
        example: ***********
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      method:
        name: method
        description: 请求方法筛选
        required: false
        example: POST
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      path:
        name: path
        description: 请求路径搜索
        required: false
        example: /api/users
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      operation_type:
        name: operation_type
        description: 操作类型筛选
        required: false
        example: create
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      target_type:
        name: target_type
        description: 目标类型筛选
        required: false
        example: Asset
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      menu_name:
        name: menu_name
        description: 菜单名称搜索
        required: false
        example: 资产管理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 开始时间
        required: false
        example: '2024-01-01 00:00:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 结束时间
        required: false
        example: '2024-12-31 23:59:59'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      user_name: admin
      ip: ***********
      method: POST
      path: /api/users
      operation_type: create
      target_type: Asset
      menu_name: 资产管理
      start_time: '2024-01-01 00:00:00'
      end_time: '2024-12-31 23:59:59'
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/operation-logs/{operationLog_id}'
    metadata:
      groupName: 操作日志管理
      groupDescription: |-

        系统操作日志查询接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取操作日志详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      operationLog_id:
        name: operationLog_id
        description: 'The ID of the operationLog.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 日志ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      operationLog_id: 16
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/operation-logs/stats/operation-types
    metadata:
      groupName: 操作日志管理
      groupDescription: |-

        系统操作日志查询接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取操作类型统计
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/operation-logs/stats/menus
    metadata:
      groupName: 操作日志管理
      groupDescription: |-

        系统操作日志查询接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取菜单操作统计
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
