{"version": "pest_3.8.2", "defects": {"Tests\\Feature\\RoleMenuPermissionTest::test_admin_role_has_all_permissions": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_role_has_limited_permissions": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_can_check_menu_access": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_user_can_check_menu_permission": 8, "Tests\\Feature\\RoleMenuPermissionTest::test_role_menu_permission_api_endpoints": 8, "AssetTest::test_asset_can_be_created_with_category_ids": 8, "AssetTest::test_asset_category_accessor_returns_categories": 8, "AssetTest::test_asset_can_load_relationships_without_error": 8, "Tests\\Feature\\CheckinConfigRouteBindingTest::test_checkin_config_route_binding_works": 8, "Tests\\Feature\\CheckinConfigRouteBindingTest::test_checkin_config_route_binding_fails_for_nonexistent_id": 8}, "times": {"P\\Tests\\Unit\\ExampleTest::__pest_evaluable_that_true_is_true": 0.012, "P\\Tests\\Feature\\ExampleTest::__pest_evaluable_the_application_returns_a_successful_response": 0.05}}