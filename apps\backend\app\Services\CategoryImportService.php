<?php

namespace App\Services;

use App\Models\Category;

use App\Models\ImportTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class CategoryImportService
{
    protected array $errors = [];

    protected int $totalRows = 0;

    protected int $successRows = 0;

    protected int $failedRows = 0;

    protected array $processedCategories = [];

    /**
     * 处理分类导入
     */
    public function processImport(ImportTask $importTask): array
    {
        $this->resetCounters();

        try {
            // 获取文件路径
            $filePath = $this->getActualFilePath($importTask->file_path);

            // 读取Excel文件
            $data = Excel::toArray(new class {}, $filePath);

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空或格式不正确');
            }

            $rows = $data[0];

            // 查找标题行
            $headerRowIndex = $this->findHeaderRow($rows);
            if ($headerRowIndex === -1) {
                throw new \Exception('未找到有效的标题行');
            }

            // 获取标题映射
            $headers = $rows[$headerRowIndex];
            $headerMap = $this->buildHeaderMap($headers);

            // 移除标题行之前的所有行，只保留数据行
            $dataRows = array_slice($rows, $headerRowIndex + 1);
            $this->totalRows = count($dataRows);

            if ($this->totalRows <= 0) {
                throw new \Exception('Excel文件中没有数据行');
            }

            Log::info('开始处理分类导入', [
                'total_rows' => $this->totalRows,
                'file_path' => $filePath,
            ]);

            // 多轮处理数据以支持层级关系
            $this->processDataInRounds($dataRows, $headerMap, $headerRowIndex + 2);

            // 更新任务进度
            $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

            return [
                'total_rows' => $this->totalRows,
                'success_rows' => $this->successRows,
                'failed_rows' => $this->failedRows,
                'errors' => $this->errors,
                'summary' => $this->generateSummary(),
            ];

        } catch (\Exception $e) {
            Log::error('分类导入失败', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
            ]);

            throw $e;
        }
    }

    /**
     * 查找标题行
     */
    protected function findHeaderRow(array $rows): int
    {
        $requiredHeaders = ['分类名', '分类编码'];

        foreach ($rows as $index => $row) {
            if (empty($row)) {
                continue;
            }

            $foundHeaders = 0;
            foreach ($row as $cell) {
                if (in_array(trim($cell), $requiredHeaders)) {
                    $foundHeaders++;
                }
            }

            if ($foundHeaders >= count($requiredHeaders)) {
                return $index;
            }
        }

        return -1;
    }

    /**
     * 构建标题映射
     */
    protected function buildHeaderMap(array $headers): array
    {
        $map = [];
        $fieldMapping = [
            '分类名' => 'name',
            '分类名称' => 'name',
            '上级分类' => 'parent_name',
            '父级分类' => 'parent_name',
            '分类编码' => 'code',
            '编码' => 'code',
            '备注' => 'remark',
            '说明' => 'remark',
        ];

        foreach ($headers as $index => $header) {
            $header = trim($header);
            if (isset($fieldMapping[$header])) {
                $map[$fieldMapping[$header]] = $index;
            }
        }

        return $map;
    }

    /**
     * 多轮处理数据以支持层级关系
     */
    protected function processDataInRounds(array $dataRows, array $headerMap, int $startRowNumber): void
    {
        $remainingRows = [];
        $maxRounds = 10; // 最多处理10轮，避免无限循环
        $round = 1;

        // 准备所有行的数据
        foreach ($dataRows as $index => $row) {
            $remainingRows[] = [
                'row' => $row,
                'row_number' => $startRowNumber + $index,
            ];
        }

        while (! empty($remainingRows) && $round <= $maxRounds) {
            Log::info("开始第 {$round} 轮分类处理", [
                'remaining_rows' => count($remainingRows),
                'round' => $round,
            ]);

            $processedInThisRound = [];
            $failedInThisRound = [];

            foreach ($remainingRows as $item) {
                try {
                    $categoryData = $this->prepareCategoryData($item['row'], $headerMap, $item['row_number']);
                    if ($categoryData) {
                        // 尝试创建分类
                        $category = Category::create($categoryData);

                        // 缓存新创建的分类
                        $this->processedCategories[$category->name] = $category;
                        $this->successRows++;

                        Log::info('分类创建成功', [
                            'round' => $round,
                            'row' => $item['row_number'],
                            'category_id' => $category->id,
                            'name' => $category->name,
                            'code' => $category->code,
                        ]);

                        $processedInThisRound[] = $item;
                    }
                } catch (\Exception $e) {
                    // 如果是找不到父分类的错误，留到下一轮处理
                    if (strpos($e->getMessage(), '找不到上级分类') !== false) {
                        $failedInThisRound[] = $item;
                    } else {
                        // 其他错误直接记录
                        $this->failedRows++;
                        $this->errors[] = [
                            'row' => $item['row_number'],
                            'error' => $e->getMessage(),
                            'data' => $item['row'],
                        ];
                    }
                }
            }

            // 如果这一轮没有处理任何数据，说明剩余的都是无法解决的依赖问题
            if (empty($processedInThisRound)) {
                foreach ($failedInThisRound as $item) {
                    $this->failedRows++;
                    $this->errors[] = [
                        'row' => $item['row_number'],
                        'error' => '无法解析层级关系，可能存在循环依赖或父分类不存在',
                        'data' => $item['row'],
                    ];
                }
                break;
            }

            // 准备下一轮处理
            $remainingRows = $failedInThisRound;
            $round++;
        }

        // 如果达到最大轮数仍有未处理的数据
        if (! empty($remainingRows)) {
            foreach ($remainingRows as $item) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $item['row_number'],
                    'error' => '达到最大处理轮数，可能存在复杂的层级依赖关系',
                    'data' => $item['row'],
                ];
            }
        }

        Log::info('分类多轮处理完成', [
            'total_rounds' => $round - 1,
            'success_rows' => $this->successRows,
            'failed_rows' => $this->failedRows,
        ]);
    }

    /**
     * 处理批次数据
     */
    protected function processBatch(array $batch, array $headerMap, int $startRowNumber): void
    {
        $failedRowsBefore = $this->failedRows;

        try {
            DB::beginTransaction();

            Log::info('开始处理分类批次', [
                'batch_size' => count($batch),
                'start_row' => $startRowNumber,
            ]);

            // 收集分类数据
            $categories = [];

            foreach ($batch as $index => $row) {
                $rowNumber = $startRowNumber + $index;

                try {
                    $categoryData = $this->prepareCategoryData($row, $headerMap, $rowNumber);
                    if ($categoryData) {
                        $categories[] = $categoryData;
                    }
                } catch (\Exception $e) {
                    $this->failedRows++;
                    $this->errors[] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $row,
                    ];
                }
            }

            // 批量插入分类数据
            $this->batchInsertCategories($categories);

            // 计算当前批次的成功数量
            $batchFailedCount = $this->failedRows - $failedRowsBefore;
            $batchSuccessCount = count($batch) - $batchFailedCount;
            $this->successRows += $batchSuccessCount;

            DB::commit();

            Log::info('分类批次处理完成', [
                'batch_size' => count($batch),
                'batch_success' => $batchSuccessCount,
                'batch_failed' => $batchFailedCount,
                'total_success' => $this->successRows,
                'total_failed' => $this->failedRows,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            // 将整个批次标记为失败
            foreach ($batch as $index => $item) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $startRowNumber + $index,
                    'error' => '批量处理失败: '.$e->getMessage(),
                    'data' => $item,
                ];
            }

            Log::error('分类批次处理失败', [
                'error' => $e->getMessage(),
                'start_row' => $startRowNumber,
            ]);
        }
    }

    /**
     * 准备分类数据
     */
    protected function prepareCategoryData(array $row, array $headerMap, int $rowNumber): ?array
    {
        // 提取数据
        $name = trim($row[$headerMap['name']] ?? '');
        $code = trim($row[$headerMap['code']] ?? '');
        $parentName = trim($row[$headerMap['parent_name']] ?? '');
        $remark = trim($row[$headerMap['remark']] ?? '');

        // 验证必填字段
        if (empty($name)) {
            throw new \Exception("第{$rowNumber}行：分类名不能为空");
        }

        if (empty($code)) {
            throw new \Exception("第{$rowNumber}行：分类编码不能为空");
        }

        // 验证编码格式
        if (! preg_match('/^[a-zA-Z0-9_-]+$/', $code)) {
            throw new \Exception("第{$rowNumber}行：分类编码只能包含字母、数字、下划线和横线");
        }

        // 检查编码是否已存在
        if (Category::where('code', $code)->exists()) {
            throw new \Exception("第{$rowNumber}行：分类编码 '{$code}' 已存在");
        }

        // 处理父级分类
        $parentId = 0;
        $level = 1;

        if (! empty($parentName)) {
            $parent = $this->findParentCategory($parentName);
            if (! $parent) {
                throw new \Exception("第{$rowNumber}行：找不到上级分类 '{$parentName}'");
            }
            $parentId = $parent->id;
            $level = $parent->level + 1;
        }

        return [
            'name' => $name,
            'code' => $code,
            'parent_id' => $parentId,
            'level' => $level,
            'sort' => 0,
            'status' => 1,
            'remark' => $remark ?: null,
            '_row_number' => $rowNumber,
        ];
    }

    /**
     * 查找父级分类
     */
    protected function findParentCategory(string $parentName): ?Category
    {
        // 先从已处理的分类中查找
        if (isset($this->processedCategories[$parentName])) {
            return $this->processedCategories[$parentName];
        }

        // 从数据库中查找
        $parent = Category::where('name', $parentName)->first();
        if ($parent) {
            $this->processedCategories[$parentName] = $parent;
        }

        return $parent;
    }

    /**
     * 批量插入分类数据
     */
    protected function batchInsertCategories(array $categories): void
    {
        if (empty($categories)) {
            return;
        }

        foreach ($categories as $categoryData) {
            $rowNumber = $categoryData['_row_number'];
            unset($categoryData['_row_number']);

            try {
                $category = Category::create($categoryData);

                // 缓存新创建的分类
                $this->processedCategories[$category->name] = $category;

                Log::info('分类创建成功', [
                    'row' => $rowNumber,
                    'category_id' => $category->id,
                    'name' => $category->name,
                    'code' => $category->code,
                ]);

            } catch (\Exception $e) {
                throw new \Exception("第{$rowNumber}行：创建分类失败 - ".$e->getMessage());
            }
        }
    }

    /**
     * 重置计数器
     */
    protected function resetCounters(): void
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;
        $this->processedCategories = [];
    }

    /**
     * 生成摘要信息
     */
    protected function generateSummary(): string
    {
        return sprintf(
            '分类导入完成：总计 %d 行，成功 %d 行，失败 %d 行',
            $this->totalRows,
            $this->successRows,
            $this->failedRows
        );
    }

    /**
     * 获取实际文件路径
     */
    protected function getActualFilePath(string $filePath): string
    {
        // 如果是绝对路径且文件存在，直接返回
        if (file_exists($filePath)) {
            return $filePath;
        }

        // 处理附件系统的相对路径
        $possiblePaths = [
            // Laravel public disk 路径 (storage/app/public/)
            storage_path('app/public/'.$filePath),
            // Laravel local disk 路径 (storage/app/)
            storage_path('app/'.$filePath),
            // 公共存储路径 (public/storage/)
            public_path('storage/'.$filePath),
            // 直接在public目录
            public_path($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都不存在，记录日志并返回原路径
        Log::warning('导入文件路径查找失败', [
            'original_path' => $filePath,
            'tried_paths' => $possiblePaths,
        ]);

        return $filePath;
    }
}
