<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessAssetImport;
use App\Jobs\ProcessCategoryImport;
use App\Jobs\ProcessEntityImport;
use App\Jobs\ProcessUserImport;
use App\Models\AssetImportTask;
use App\Models\Attachment;
use App\Models\CategoryImportTask;
use App\Models\EntityImportTask;
use App\Models\UserImportTask;
use Illuminate\Http\JsonResponse;

class ImportController extends Controller
{
	/**
	 * 统一创建导入任务
	 *
	 * 路径参数:
	 * - type: 导入类型，支持 asset/category/entity/user
	 * - attachment: 附件ID
	 */
	public function import(string $type, Attachment $attachment): JsonResponse
	{
		$type = strtolower($type);

		switch ($type) {
			case 'asset':
				$task = AssetImportTask::create([
					'file_path' => $attachment->file_path,
					'original_filename' => $attachment->file_name,
					'status' => 'pending',
					'created_by' => auth()->id(),
				]);
				ProcessAssetImport::dispatch($task);
				break;
			case 'category':
				$task = CategoryImportTask::create([
					'file_path' => $attachment->file_path,
					'original_filename' => $attachment->file_name,
					'status' => CategoryImportTask::STATUS_PENDING,
					'created_by' => auth()->id(),
				]);
				ProcessCategoryImport::dispatch($task);
				break;
			case 'entity':
				$task = EntityImportTask::create([
					'file_path' => $attachment->file_path,
					'original_filename' => $attachment->file_name,
					'status' => 'pending',
					'created_by' => auth()->id(),
				]);
				ProcessEntityImport::dispatch($task);
				break;
			case 'user':
				$task = UserImportTask::create([
					'file_path' => $attachment->file_path,
					'original_filename' => $attachment->file_name,
					'status' => 'pending',
					'created_by' => auth()->id(),
				]);
				ProcessUserImport::dispatch($task);
				break;
			default:
				return response()->json(['message' => '不支持的导入类型'], 422);
		}

		return response()->json([
			'message' => '导入任务已创建，正在后台处理',
			'task_id' => $task->id,
			'type' => $type,
		], 201);
	}

	/**
	 * 统一查询导入任务状态
	 *
	 * 路径参数:
	 * - type: 导入类型
	 * - task: 任务ID
	 */
	public function status(string $type, int $task): JsonResponse
	{
		$type = strtolower($type);

		switch ($type) {
			case 'asset':
				$model = AssetImportTask::findOrFail($task);
				return response()->json($this->normalize($model));
			case 'category':
				$model = CategoryImportTask::findOrFail($task);
				return response()->json($this->normalize($model));
			case 'entity':
				$model = EntityImportTask::findOrFail($task);
				return response()->json($this->normalize($model));
			case 'user':
				$model = UserImportTask::findOrFail($task);
				return response()->json($this->normalize($model));
			default:
				return response()->json(['message' => '不支持的导入类型'], 422);
		}
	}

	/**
	 * 规范化不同任务模型的响应结构
	 */
	private function normalize($task): array
	{
		// 兼容不同字段命名
		$errorDetails = $task->error_details ?? null;
		if (! $errorDetails && property_exists($task, 'errors')) {
			$errorDetails = $task->errors;
		}
		if (! $errorDetails && property_exists($task, 'error_message')) {
			// 可能为JSON字符串或纯文本
			$decoded = json_decode($task->error_message, true);
			$errorDetails = json_last_error() === JSON_ERROR_NONE ? $decoded : [['error' => (string) $task->error_message]];
		}

		return [
			'id' => $task->id,
			'file_path' => $task->file_path,
			'original_filename' => $task->original_filename,
			'status' => $task->status,
			'total_rows' => (int) ($task->total_rows ?? 0),
			'success_rows' => (int) ($task->success_rows ?? 0),
			'failed_rows' => (int) ($task->failed_rows ?? 0),
			'error_details' => $errorDetails,
			'summary' => $task->summary ?? null,
			'started_at' => $task->started_at ?? null,
			'completed_at' => $task->completed_at ?? null,
			'created_at' => $task->created_at ?? null,
			'updated_at' => $task->updated_at ?? null,
		];
	}
}
