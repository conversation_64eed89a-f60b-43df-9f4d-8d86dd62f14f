<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\ImportTaskResource;
use App\Jobs\ProcessAssetImport;
use App\Jobs\ProcessCategoryImport;
use App\Jobs\ProcessEntityImport;
use App\Jobs\ProcessUserImport;
use App\Models\Attachment;
use App\Models\ImportTask;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class ImportController extends Controller
{
	/**
	 * 统一创建导入任务
	 *
	 * 路径参数:
	 * - type: 导入类型，支持 asset/category/entity/user
	 * - attachment: 附件ID
	 */
	public function import(string $type, Attachment $attachment): JsonResponse
	{
		$type = strtolower($type);

		// 验证导入类型
		$supportedTypes = ['asset', 'category', 'entity', 'user'];
		if (!in_array($type, $supportedTypes)) {
			return response()->json(['message' => '不支持的导入类型'], 422);
		}

		try {
			// 创建统一的导入任务
			$task = ImportTask::create([
				'type' => $type,
				'file_path' => $attachment->file_path,
				'original_filename' => $attachment->file_name,
				'status' => ImportTask::STATUS_PENDING,
				'created_by' => auth()->id(),
			]);

			// 根据类型分发到对应的Job
			switch ($type) {
				case 'asset':
					ProcessAssetImport::dispatch($task);
					break;
				case 'category':
					ProcessCategoryImport::dispatch($task);
					break;
				case 'entity':
					ProcessEntityImport::dispatch($task);
					break;
				case 'user':
					ProcessUserImport::dispatch($task);
					break;
			}

			Log::info('导入任务已创建', [
				'task_id' => $task->id,
				'type' => $type,
				'file_path' => $attachment->file_path,
				'created_by' => auth()->id(),
			]);

			return response()->json([
				'message' => '导入任务已创建，正在后台处理',
				'task_id' => $task->id,
				'type' => $type,
			], 201);

		} catch (\Exception $e) {
			Log::error('创建导入任务失败', [
				'type' => $type,
				'attachment_id' => $attachment->id,
				'file_path' => $attachment->file_path,
				'file_name' => $attachment->file_name,
				'file_size' => $attachment->file_size ?? 'unknown',
				'created_by' => auth()->id(),
				'error' => $e->getMessage(),
				'exception_type' => get_class($e),
				'trace' => $e->getTraceAsString(),
			]);

			// 根据异常类型返回不同的错误信息
			$message = '创建导入任务失败';
			$statusCode = 500;

			if ($e instanceof \Illuminate\Database\QueryException) {
				$message = '数据库操作失败，请稍后重试';
			} elseif ($e instanceof \InvalidArgumentException) {
				$message = '参数错误：' . $e->getMessage();
				$statusCode = 422;
			} else {
				$message = '创建导入任务失败：' . $e->getMessage();
			}

			return response()->json([
				'message' => $message,
				'error_code' => 'IMPORT_TASK_CREATE_FAILED',
				'timestamp' => date('Y-m-d H:i:s'),
			], $statusCode);
		}
	}

	/**
	 * 统一查询导入任务状态
	 *
	 * 路径参数:
	 * - type: 导入类型
	 * - task: 任务ID
	 */
	public function status(string $type, int $task): JsonResponse|ImportTaskResource
	{
		$type = strtolower($type);

		// 验证导入类型
		$supportedTypes = ['asset', 'category', 'entity', 'user'];
		if (!in_array($type, $supportedTypes)) {
			return response()->json(['message' => '不支持的导入类型'], 422);
		}

		try {
			// 查找指定类型和ID的导入任务
			$importTask = ImportTask::where('type', $type)
				->where('id', $task)
				->with('creator')
				->firstOrFail();

			return new ImportTaskResource($importTask);

		} catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
			Log::warning('导入任务不存在', [
				'type' => $type,
				'task_id' => $task,
				'requested_by' => auth()->id(),
			]);

			return response()->json([
				'message' => '导入任务不存在',
				'error_code' => 'IMPORT_TASK_NOT_FOUND',
			], 404);

		} catch (\Exception $e) {
			Log::error('查询导入任务状态失败', [
				'type' => $type,
				'task_id' => $task,
				'requested_by' => auth()->id(),
				'error' => $e->getMessage(),
				'exception_type' => get_class($e),
				'trace' => $e->getTraceAsString(),
			]);

			return response()->json([
				'message' => '查询导入任务状态失败：' . $e->getMessage(),
				'error_code' => 'IMPORT_TASK_QUERY_FAILED',
				'timestamp' => date('Y-m-d H:i:s'),
			], 500);
		}
	}



	/**
	 * 获取指定类型的导入任务列表
	 *
	 * 路径参数:
	 * - type: 导入类型
	 */
	public function list(string $type)
	{
		$type = strtolower($type);

		// 验证导入类型
		$supportedTypes = ['asset', 'category', 'entity', 'user'];
		if (!in_array($type, $supportedTypes)) {
			return response()->json(['message' => '不支持的导入类型'], 422);
		}

		try {
			$tasks = ImportTask::where('type', $type)
				->with('creator')
				->orderBy('created_at', 'desc')
				->paginate(20);

			return ImportTaskResource::collection($tasks);

		} catch (\Exception $e) {
			Log::error('获取导入任务列表失败', [
				'type' => $type,
				'error' => $e->getMessage(),
			]);

			return response()->json([
				'message' => '获取导入任务列表失败：' . $e->getMessage()
			], 500);
		}
	}
}
