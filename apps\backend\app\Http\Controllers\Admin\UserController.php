<?php

namespace App\Http\Controllers\Admin;

use App\Exports\UserTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserRequest;
use App\Http\Resources\Admin\UserResource;
use App\Jobs\ProcessUserImport;
use App\Models\Attachment;
use App\Models\ImportTask;
use App\Models\User;
use App\Services\UserImportService;
use App\Services\UserService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @group 用户管理
 */
class UserController extends Controller
{
    public function __construct(
        private UserService $userService,
        private UserImportService $userImportService
    ) {}

    /**
     * 获取用户列表
     *
     * @queryParam keyword string 搜索关键词（用户名、手机号、邮箱）Example: admin
     * @queryParam status string 用户状态（enable/disable） Example: enable
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     *
     * @apiResourceCollection App\Http\Resources\Admin\UserResource
     *
     * @apiResourceModel App\Models\User paginate=20 with=attachments,roles
     */
    public function index(Request $request)
    {
        $users = $this->userService->paginate($request->all());

        return UserResource::collection($users);
    }

    /**
     * 创建用户
     *
     * @bodyParam account required string 用户账号 Example: ***********
     * @bodyParam password string required 密码（最少6位） Example: password123
     * @bodyParam nickname string 用户昵称 Example: testuser
     * @bodyParam email string 邮箱 Example: <EMAIL>
     * @bodyParam status string 状态（enable/disable） Example: enable
     *
     * @response 201 {
     *   "data": {
     *     "id": 1,
     *     "account": "***********",
     *     "nickname": "testuser",
     *     "email": "<EMAIL>"
     *   }
     * }
     */
    public function store(UserRequest $request)
    {
        $user = $this->userService->create($request->validated());

        return (new UserResource($user))->response()->setStatusCode(201);
    }

    /**
     * 获取用户详情
     *
     * @urlParam user int required 用户ID Example: 1
     *
     * @return UserResource
     */
    public function show(User $user)
    {
        $user->load('attachments');

        return new UserResource($user);
    }

    /**
     * 更新用户
     *
     * @urlParam user int required 用户ID Example: 1
     *
     * @bodyParam nickname string 昵称 Example: testuser
     * @bodyParam password string 密码（为空则不更新） Example: newpassword
     * @bodyParam email string 邮箱 Example: <EMAIL>
     * @bodyParam account string 账号 Example: ***********
     * @bodyParam status string 状态（enable/disable） Example: enable
     *
     * @return UserResource
     */
    public function update(UserRequest $request, User $user)
    {
        $user = $this->userService->update($user, $request->validated());

        return new UserResource($user);
    }

    /**
     * 删除用户
     *
     * @urlParam user int required 用户ID Example: 1
     *
     * @response 204
     */
    public function destroy(User $user)
    {
        // 防止删除自己
        if ($user->id === auth()->id()) {
            return response()->json(['message' => '不能删除自己'], 403);
        }

        $this->userService->delete($user);

        return response()->noContent();
    }

    /**
     * 导出用户Excel模板
     *
     * 导出包含字段：用户账号、密码、用户昵称、邮箱、用户角色
     */
    public function exportTemplate()
    {
        $filename = '用户导入模板_'.date('Y-m-d_H-i-s').'.xlsx';

        return Excel::download(new UserTemplateExport, $filename);
    }
}
