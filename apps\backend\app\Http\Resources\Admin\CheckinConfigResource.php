<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckinConfigResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        // 如果有关联信息，添加第一个关联的信息
        if ($this->relationLoaded('attachables') && $this->attachables->isNotEmpty()) {
            $firstRelation = $this->attachables->first();
            $data['relation_id'] = (string) $firstRelation->id;
            $data['attachable_type'] = $firstRelation->attachable_type;
            $data['attachable_id'] = (string) $firstRelation->attachable_id;
            $data['category'] = $firstRelation->category;
            $data['description'] = $firstRelation->description;
        } else {
            // 为了前端兼容，提供默认值
            $data['relation_id'] = null;
            $data['attachable_type'] = null;
            $data['attachable_id'] = null;
            $data['category'] = null;
            $data['description'] = null;
        }

        $data = [
            'id' => $this->id,
            'attachable_type' => $this->attachable_type,
            'attachable_id' => $this->attachable_id,
            'checkin_time' => $this->checkin_time,
            'status' => $this->status,
            'is_photo' => $this->is_photo,
            'location' => $this->location,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'location_range' => $this->location_range,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'users' => $this->users,
            'attachable' => $this->attachable,
        ];

        return $data;
    }
}
