// 生命周期管理相关类型定义

import type { Asset } from './asset'
import type { Tag } from './tag'

// 生命周期关联资产信息
export interface LifecycleAsset {
  id: number // 资产ID
  notes?: string // 备注信息
}

// 生命周期实体
export interface Lifecycle {
  id?: number
  // 多资产支持：新的assets字段替代原来的asset_id
  assets?: LifecycleAsset[] // 关联的多个资产（包含备注）
  asset_details?: Asset[] // 资产详情信息（用于显示）
  // 为了兼容性，保留原字段但标记为废弃
  asset_id?: number | null // @deprecated 资产ID（向后兼容）
  asset?: Asset // @deprecated 关联的资产信息（向后兼容）
  type: string | null // 类型（字典值）
  date: string // 日期
  // 新的字段名（与后端API一致）
  initiator_id?: number | null // 发起人ID（新字段名）
  acceptance_entity_id?: number | null // 验收相关方ID（新字段名）
  acceptance_personnel_id?: number | null // 验收人员ID（新字段名）
  // 向后兼容的字段名
  initiator?: number | null // @deprecated 发起人ID（向后兼容）
  acceptance_entity?: number | null // @deprecated 验收相关方ID（向后兼容）
  acceptance_personnel?: number | null // @deprecated 验收人员ID（向后兼容）
  // 显示用字段
  initiator_name?: string // 发起人名称
  content: string // 内容
  assistants: number[] // 协助人员ID数组
  assistant_names?: string[] // 协助人员名称数组
  acceptance_entity_name?: string // 验收相关方名称
  acceptance_personnel_name?: string // 验收人员名称
  acceptance_time: string // 验收时间
  attachments: number[] // 相关文件ID数组
  attachment_details?: Array<{
    id: number
    file_name: string
    file_url: string
    file_size?: number
    file_type?: string
  }> // 附件详情
  // 标签系统
  tag_ids?: number[] // 关联的标签ID数组（提交时使用）
  tags?: Tag[] // 标签详情数组（后端返回的完整标签对象）
  tag_details?: Tag[] // 标签详情（用于显示，兼容字段）
  // 验收和进度系统
  is_checked?: boolean // 验收状态
  progress?: number // 进度（0-100）
  // 跟进记录
  follow_ups?: LifecycleFollowUp[] // 跟进记录列表（详情接口返回）
  created_at?: string // 创建时间
  updated_at?: string // 更新时间
}

// 生命周期跟进记录
export interface LifecycleFollowUp {
  id?: number
  lifecycle_id: number | null // 周期表ID
  date: string // 日期
  person_id: number | null // 人员ID（只能是协助人员中的）
  person_name?: string // 人员名称（用于显示）
  content: string // 内容
  created_at?: string // 添加时间
  attachments: number[] // 相关文件ID数组
  attachment_details?: Array<{
    id: number
    file_name: string
    file_url: string
    file_size?: number
    file_type?: string
  }> // 附件详情
  // 跟进标签系统
  tag_ids?: number[] // 关联的标签ID数组（提交时使用，只能是生命周期已有的标签）
  tags?: number[] // 关联的标签ID数组（兼容字段）
  tag_details?: Tag[] // 标签详情（用于显示）
}

// 生命周期创建/更新请求数据
export interface LifecycleFormData {
  assets: number[] // 资产ID数组（后端期望格式）
  type: string // 类型
  date: string // 日期
  initiator_id: number // 发起人ID
  content: string // 内容
  assistants: number[] // 协助人员ID数组
  acceptance_entity_id: number // 验收相关方ID
  acceptance_personnel_id: number // 验收人员ID
  acceptance_time: string // 验收时间
  attachments?: number[] // 附件ID数组
  tag_ids?: number[] // 标签ID数组
}

// 跟进记录创建/更新请求数据
export interface FollowUpFormData {
  date: string // 日期
  person_id: number // 人员ID
  content: string // 内容
  attachments?: number[] // 附件ID数组
  tag_ids?: number[] // 标签ID数组（只能选择生命周期已有的标签）
}
