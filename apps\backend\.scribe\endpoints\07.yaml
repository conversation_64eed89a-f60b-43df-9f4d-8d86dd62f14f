name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: 'api/admin/import/{type}/{attachment_id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 统一创建导入任务
      description: |-
        路径参数:
        - type: 导入类型，支持 asset/category/entity/user
        - attachment: 附件ID
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      type:
        name: type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachment_id:
        name: attachment_id
        description: 'The ID of the attachment.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      type: architecto
      attachment_id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/import/{type}/tasks/{task}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 统一查询导入任务状态
      description: |-
        路径参数:
        - type: 导入类型
        - task: 任务ID
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      type:
        name: type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      task:
        name: task
        description: 'The task.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      type: architecto
      task: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
