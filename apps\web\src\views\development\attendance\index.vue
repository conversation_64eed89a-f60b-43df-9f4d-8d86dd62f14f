<template>
  <div class="attendance-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
      :showExpand="true"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton type="primary" @click="showConfigDialog('add')" v-ripple v-if="hasAuth('add')">
            <ElIcon><Plus /></ElIcon>
            新增考勤配置
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 状态列插槽 -->
        <template #status="{ row }">
          <ElTag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </ElTag>
        </template>

        <!-- 打卡地点列插槽 -->
        <template #location="{ row }">
          <ElTooltip :content="row.location_address" placement="top">
            <span class="location-text">{{ row.location_address }}</span>
          </ElTooltip>
        </template>

        <!-- 打卡范围列插槽 -->
        <template #location_radius="{ row }">
          <span>{{ row.location_radius }}米</span>
        </template>

        <!-- 打卡时间列插槽 -->
        <template #checkin_time="{ row }">
          <span>{{ row.checkin_time || '-' }}</span>
        </template>

        <!-- 参与人员列插槽 -->
        <template #participants="{ row }">
          <div v-if="row.participants && row.participants.length > 0">
            <ElPopover placement="right" width="400" trigger="hover">
              <template #reference>
                <ElButton type="primary" link>
                  <ElIcon><User /></ElIcon>
                  {{ row.participants.length }}人
                </ElButton>
              </template>
              <template #default>
                <div>
                  <h4
                    style="margin: 0 0 10px; font-size: 14px; color: var(--el-text-color-primary)"
                  >
                    参与人员
                  </h4>
                  <div class="participants-list">
                    <div
                      v-for="participant in row.participants"
                      :key="participant.id"
                      class="participant-item"
                    >
                      <ElAvatar
                        :src="participant.avatar"
                        :size="24"
                        :icon="UserFilled"
                        class="participant-avatar"
                      />
                      <span class="participant-name">{{ participant.name }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </ElPopover>
          </div>
          <span v-else>-</span>
        </template>

        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; flex-wrap: wrap; gap: 5px">
            <ArtButtonTable type="primary" @click="showRecordDialog(row)">
              打卡记录
            </ArtButtonTable>
            <ArtButtonTable
              type="edit"
              @click="showConfigDialog('edit', row)"
              v-if="hasAuth('edit')"
            />
            <ArtButtonTable type="delete" @click="handleDelete(row)" v-if="hasAuth('delete')" />
          </div>
        </template>
      </ArtTable>

      <!-- 考勤配置对话框 -->
      <AttendanceConfigDialog
        v-model="configDialogVisible"
        :type="dialogType"
        :data="currentConfig"
        @success="handleConfigSuccess"
      />

      <!-- 打卡记录对话框 -->
      <CheckinRecordDialog
        v-model="recordDialogVisible"
        :config="currentConfig"
        @success="handleRecordSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Attendance' })

  // Vue 核心
  import { ref, computed } from 'vue'

  // UI 框架
  import {
    ElButton,
    ElMessage,
    ElMessageBox,
    ElIcon,
    ElTag,
    ElPopover,
    ElTooltip,
    ElAvatar
  } from 'element-plus'
  import { Plus, User, UserFilled } from '@element-plus/icons-vue'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import AttendanceConfigDialog from './components/AttendanceConfigDialog.vue'
  import CheckinRecordDialog from './components/CheckinRecordDialog.vue'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'

  // API
  import {
    getCheckinConfigs,
    deleteCheckinConfig,
    getCheckinConfig
  } from '@/api/admin/attendance'
  import type { CheckinConfig, CheckinConfigSearchParams } from '@/types/api/attendance'

  // 类型定义
  import type { SearchFormItem } from '@/types'

  // 表格配置
  const tableConfig = {
    rowKey: 'id',
    height: undefined,
    maxHeight: '600px'
  }

  // 权限控制
  const { hasAuth } = useAuth()

  // 对话框
  const configDialogVisible = ref(false)
  const recordDialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const currentConfig = ref<CheckinConfig | null>(null)

  // 搜索表单状态
  const searchFormState = ref({
    status: undefined
  })

  // 搜索表单配置
  const searchFormItems = computed<SearchFormItem[]>(() => [
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ]
    }
  ])

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<CheckinConfig>({
    core: {
      apiFn: async (params: any) => {
        // 处理参数格式
        const searchParams: CheckinConfigSearchParams = {}
        if (params.status) {
          searchParams.status = params.status
        }

        const response = await getCheckinConfigs({
          page: params.current,
          per_page: params.size,
          ...searchParams
        })

        return {
          records: response.data || [],
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10,
        status: ''
      },
      columnsFactory: () => [
        { prop: 'id', label: 'ID', width: 100 },
        {
          prop: 'location',
          label: '打卡地点',
          minWidth: 250,
          useSlot: true,
          slotName: 'location'
        },
        {
          prop: 'location_radius',
          label: '打卡范围',
          width: 100,
          useSlot: true,
          slotName: 'location_radius'
        },
        {
          prop: 'checkin_time',
          label: '打卡时间',
          width: 120,
          useSlot: true,
          slotName: 'checkin_time'
        },
        {
          prop: 'participants',
          label: '参与人员',
          width: 100,
          useSlot: true,
          slotName: 'participants'
        },
        {
          prop: 'status',
          label: '状态',
          width: 80,
          useSlot: true,
          slotName: 'status'
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 150,
          formatter: (row: CheckinConfig) =>
            row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm') : '-'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 240,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: true
    }
  })

  // 处理搜索
  const handleSearch = () => {
    searchState.status = searchFormState.value.status
    searchData()
  }

  // 处理重置
  const handleReset = () => {
    searchFormState.value = {
      status: undefined
    }
    resetSearch()
  }

  // 显示配置对话框
  const showConfigDialog = async (type: 'add' | 'edit', row?: CheckinConfig) => {
    dialogType.value = type
    if (type === 'edit' && row?.id) {
      // 编辑时获取详情数据
      const detail = await getCheckinConfig(row.id)
      currentConfig.value = detail
    } else {
      currentConfig.value = null
    }
    configDialogVisible.value = true
  }

  // 显示打卡记录对话框
  const showRecordDialog = (row: CheckinConfig) => {
    currentConfig.value = row
    recordDialogVisible.value = true
  }

  // 显示详情

  // 删除
  const handleDelete = async (row: CheckinConfig) => {
    try {
      await ElMessageBox.confirm('确定要删除该考勤配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await deleteCheckinConfig(row.id)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 配置对话框成功
  const handleConfigSuccess = () => {
    if (dialogType.value === 'add') {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 记录对话框成功
  const handleRecordSuccess = () => {
    // 打卡记录不影响配置列表，无需刷新
  }
</script>

<style lang="scss" scoped>
  .attendance-page {
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  .location-text {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }


  .participants-list {
    max-height: 200px;
    overflow-y: auto;

    .participant-item {
      display: flex;
      align-items: center;
      padding: 4px 0;

      .participant-avatar {
        margin-right: 8px;
      }

      .participant-name {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
    }
  }
</style>
