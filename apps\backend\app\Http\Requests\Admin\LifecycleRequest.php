<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class LifecycleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isUpdate = $this->isMethod('put') || $this->isMethod('patch');

        $rules = [
            'assets' => 'required|array',
            'type' => ($isUpdate ? 'sometimes|' : '').'required|string|max:50',
            'date' => ($isUpdate ? 'sometimes|' : '').'required|integer',
            'initiator_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:users,id',
            'content' => ($isUpdate ? 'sometimes|' : '').'required|string',
            'assistants' => ($isUpdate ? 'sometimes|' : '').'required|array',
            'assistants.*' => 'exists:users,id',
            'acceptance_entity_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:entities,id',
            'acceptance_personnel_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:entity_contacts,id',
            'acceptance_time' => ($isUpdate ? 'sometimes|' : '').'required|integer',
            'attachments' => 'nullable|array',
            'attachments.*' => 'exists:attachments,id',
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
        ];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'assets.required' => '资产不能为空',
            'assets.array' => '资产必须是数组',
            'type.required' => '类型不能为空',
            'type.max' => '类型长度不能超过100个字符',
            'date.required' => '日期不能为空',
            'date.integer' => '日期格式不正确',
            'initiator_id.required' => '发起人不能为空',
            'initiator_id.exists' => '发起人不存在',
            'content.required' => '内容不能为空',
            'assistants.required' => '协助人员不能为空',
            'assistants.array' => '协助人员必须是数组',
            'assistants.min' => '至少需要1个协助人员',
            'assistants.*.exists' => '协助人员不存在',
            'acceptance_entity_id.required' => '验收相关方不能为空',
            'acceptance_entity_id.exists' => '验收相关方不存在',
            'acceptance_personnel_id.required' => '验收人员不能为空',
            'acceptance_personnel_id.exists' => '验收人员不存在',
            'acceptance_time.required' => '验收时间不能为空',
            'acceptance_time.integer' => '验收时间格式不正确',
            'attachments.array' => '附件必须是数组',
            'attachments.*.exists' => '附件不存在',
            'tag_ids.array' => '标签必须是数组',
            'tag_ids.*.exists' => '标签不存在',
        ];
    }
}
