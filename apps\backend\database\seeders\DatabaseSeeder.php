<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // 调用各个种子文件
        $this->call([
            // 基础数据
            DictionarySeeder::class,    // 字典数据
            RegionSeeder::class,        // 省市区数据
            AssetCategorySeeder::class, // 资产分类数据
            MenuSeeder::class,          // 菜单数据
            // 用户权限相关
            UserSeeder::class,          // 用户账号
            RoleMenuPermissionSeeder::class, // 角色菜单权限关联
            UserRolesSeeder::class,     // 用户角色关联
            ConfigSeeder::class,        // 系统配置
            // 医疗测试数据
            MedicalDataSeeder::class,   // 医疗相关方、品牌、联系人、设备、标签
        ]);
    }
}
