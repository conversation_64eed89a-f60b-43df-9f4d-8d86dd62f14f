## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: 考勤记录
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/checkin-records
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取打卡记录列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      config_id:
        name: config_id
        description: 打卡配置ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_id:
        name: user_id
        description: 用户ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: '状态(0-正常,1-异常)'
        required: false
        example: 0
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_start:
        name: date_start
        description: 开始日期
        required: false
        example: '2025-08-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_end:
        name: date_end
        description: 结束日期
        required: false
        example: '2025-08-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页记录数
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      config_id: 1
      user_id: 1
      status: 0
      date_start: '2025-08-01'
      date_end: '2025-08-31'
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/checkin-records
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 打卡操作
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      checkin_config_id:
        name: checkin_config_id
        description: 打卡配置ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡位置范围
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      attachment_id:
        name: attachment_id
        description: 打卡照片附件ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      content:
        name: content
        description: 打卡备注
        required: false
        example: 正常打卡
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      checkin_config_id: 1
      location: 公司大门
      latitude: '39.9042'
      longitude: '116.4074'
      location_range: 50
      attachment_id: 1
      content: 正常打卡
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/checkin-records/{id}'
    metadata:
      groupName: 考勤记录
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取打卡记录详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 记录ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
