<?php

namespace App\Http\Controllers\Admin;

use App\Exports\EntityTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\EntityRequest;
use App\Http\Resources\Admin\EntityResource;
use App\Jobs\ProcessEntityImport;
use App\Models\Attachment;
use App\Models\Entity;
use App\Services\EntityService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @group 相关方管理
 */
class EntityController extends Controller
{
    public function __construct(
        private EntityService $entityService
    ) {}

    /**
     * 获取相关方列表
     *
     * @queryParam name string 相关方名称搜索 Example: 测试公司
     * @queryParam tax_number string 税号搜索 Example: 91110108MA01A2B3C4
     * @queryParam keywords string 特征词搜索 Example: 科技
     * @queryParam keyword string 通用搜索关键词（同时搜索名称、税号、特征词）Example: 测试
     * @queryParam entity_type string 相关方类型（字典code） Example: manufacturer
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     *
     * @apiResourceCollection App\Http\Resources\Admin\EntityResource
     *
     * @apiResourceModel App\Models\Entity paginate=20
     */
    public function index(Request $request)
    {
        $entities = $this->entityService->paginate($request->all());

        return EntityResource::collection($entities);
    }

    /**
     * 创建相关方
     *
     * @bodyParam name string required 相关方名称 Example: 测试科技有限公司
     * @bodyParam tax_number string 税号 Example: 91110108MA01A2B3C4
     * @bodyParam entity_type string required 相关方类型（字典code） Example: manufacturer
     * @bodyParam address string 地址 Example: 北京市海淀区中关村大街1号
     * @bodyParam phone string 联系电话 Example: 010-12345678
     * @bodyParam keywords string 特征词（10字以内） Example: 科技创新
     * @bodyParam remark string 备注
     * @bodyParam contacts array 联系人列表
     * @bodyParam contacts[].name string required 联系人姓名 Example: 张三
     * @bodyParam contacts[].phone string required 联系电话 Example: 13800138000
     * @bodyParam contacts[].position string 职位 Example: 总经理
     * @bodyParam contacts[].department string 部门 Example: 管理部
     * @bodyParam brands array 品牌列表
     * @bodyParam brands[].name string required 品牌名称 Example: 华为
     * @bodyParam brands[].logo_id int 品牌Logo附件ID Example: 123
     * @bodyParam brands[].description string 品牌描述 Example: 全球领先的信息与通信技术解决方案供应商
     * @bodyParam brands[].sort_order int 排序顺序 Example: 1
     */
    public function store(EntityRequest $request)
    {
        $entity = $this->entityService->create($request->validated());

        return (new EntityResource($entity))
            ->response()
            ->setStatusCode(201);
    }

    /**
     * 获取相关方详情
     *
     * @urlParam entity int required 相关方ID Example: 1
     */
    public function show(Entity $entity)
    {
        $entity->load(['contacts', 'brands.attachments', 'attachments']);

        return new EntityResource($entity);
    }

    /**
     * 更新相关方
     *
     * @urlParam entity int required 相关方ID Example: 1
     *
     * @bodyParam name string required 相关方名称 Example: 测试科技有限公司
     * @bodyParam tax_number string 税号 Example: 91110108MA01A2B3C4
     * @bodyParam entity_type string required 相关方类型（字典code） Example: manufacturer
     * @bodyParam address string 地址 Example: 北京市海淀区中关村大街1号
     * @bodyParam phone string 联系电话 Example: 010-12345678
     * @bodyParam keywords string 特征词（10字以内） Example: 科技创新
     * @bodyParam remark string 备注
     * @bodyParam contacts array 联系人列表
     * @bodyParam contacts[].id int 联系人ID（更新时需要）
     * @bodyParam contacts[].name string required 联系人姓名 Example: 张三
     * @bodyParam contacts[].phone string required 联系电话 Example: 13800138000
     * @bodyParam contacts[].position string 职位 Example: 总经理
     * @bodyParam contacts[].department string 部门 Example: 管理部
     * @bodyParam brands array 品牌列表
     * @bodyParam brands[].id int 品牌ID（更新时需要）
     * @bodyParam brands[].name string required 品牌名称 Example: 华为
     * @bodyParam brands[].logo_id int 品牌Logo附件ID Example: 123
     * @bodyParam brands[].description string 品牌描述 Example: 全球领先的信息与通信技术解决方案供应商
     * @bodyParam brands[].sort_order int 排序顺序 Example: 1
     */
    public function update(EntityRequest $request, Entity $entity)
    {
        $entity = $this->entityService->update($entity, $request->validated());

        return new EntityResource($entity);
    }

    /**
     * 删除相关方
     *
     * @urlParam entity int required 相关方ID Example: 1
     */
    public function destroy(Entity $entity)
    {
        $this->entityService->delete($entity);

        return response()->noContent();
    }

    /**
     * 导出相关方模板
     *
     * 导出包含所有字段的Excel模板文件，用于相关方批量导入
     */
    public function exportTemplate()
    {
        $filename = '相关方导入模板_'.date('Y-m-d_H-i-s').'.xlsx';

        return Excel::download(new EntityTemplateExport, $filename);
    }
}
