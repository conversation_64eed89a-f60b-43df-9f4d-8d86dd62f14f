## Autogenerated by <PERSON><PERSON><PERSON>. DO NOT MODIFY.

name: 考勤配置
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/checkin-configs
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取考勤配置列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: false
        example: lifecycles
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: '状态(0-禁用,1-启用)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 搜索关键词
        required: false
        example: 早班
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页记录数
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      attachable_type: lifecycles
      status: 1
      keyword: 早班
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/checkin-configs
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建考勤配置
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: true
        example: lifecycles
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 关联业务ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      longitude:
        name: longitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      checkin_time:
        name: checkin_time
        description: 打卡时间
        required: true
        example: 1629936000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡范围(米)
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: '状态(0-禁用,1-启用)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_photo:
        name: is_photo
        description: '是否拍照(0-否,1-是)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_ids:
        name: user_ids
        description: 打卡人员ID列表
        required: true
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      attachable_type: lifecycles
      attachable_id: 1
      location: 公司大门
      longitude: '39.9042'
      latitude: '116.4074'
      checkin_time: 1629936000
      location_range: 100
      status: 1
      is_photo: 1
      user_ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/checkin-configs/{checkinConfig}'
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新考勤配置
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      checkinConfig:
        name: checkinConfig
        description: 配置ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      checkinConfig: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: 所属模块
        required: false
        example: lifecycles
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 关联业务ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location:
        name: location
        description: 打卡地点
        required: false
        example: 公司大门
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      longitude:
        name: longitude
        description: 打卡位置经度
        required: false
        example: '39.9042'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      latitude:
        name: latitude
        description: 打卡位置纬度
        required: false
        example: '116.4074'
        type: decimal
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      checkin_time:
        name: checkin_time
        description: 打卡时间
        required: false
        example: 1629936000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      location_range:
        name: location_range
        description: 打卡范围(米)
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: '状态(0-禁用,1-启用)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_photo:
        name: is_photo
        description: '是否拍照(0-否,1-是)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_ids:
        name: user_ids
        description: 打卡人员ID列表
        required: false
        example:
          - 1
          - 2
          - 3
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      attachable_type: lifecycles
      attachable_id: 1
      location: 公司大门
      longitude: '39.9042'
      latitude: '116.4074'
      checkin_time: 1629936000
      location_range: 100
      status: 1
      is_photo: 1
      user_ids:
        - 1
        - 2
        - 3
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/checkin-configs/{checkinConfig}'
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取考勤配置详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      checkinConfig:
        name: checkinConfig
        description: 配置ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      checkinConfig: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/checkin-configs/{checkinConfig}/switch'
    metadata:
      groupName: 考勤配置
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 开启/关闭考勤配置
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      checkinConfig:
        name: checkinConfig
        description: 配置ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      checkinConfig: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: '状态(0-禁用,1-启用)'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      status: 1
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
