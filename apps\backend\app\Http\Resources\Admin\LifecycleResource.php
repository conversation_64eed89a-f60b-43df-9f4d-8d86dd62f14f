<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LifecycleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'asset_ids' => $this->whenLoaded('assets', fn () => $this->assets->pluck('id')->toArray()),
            'assets' => $this->whenLoaded('assets', fn () => $this->assets->map(fn ($a) => [
                'id' => $a->id,
                'name' => $a->name,
                'brand' => $a->brand,
                'model' => $a->model,
                'serial_number' => $a->serial_number,
                'notes' => $a->pivot->notes ?? null,
            ])),
            'type' => $this->type,
            'date' => $this->date,
            'initiator' => $this->initiator_id,
            'initiator_name' => $this->whenLoaded('initiator', fn () => $this->initiator->nickname),
            'content' => $this->content,
            'assistants' => $this->whenLoaded('assistants', fn () => $this->assistants->pluck('id')->toArray()),
            'assistant_names' => $this->whenLoaded('assistants', fn () => $this->assistants->pluck('nickname')->toArray()),
            'acceptance_entity' => $this->acceptance_entity_id,
            'acceptance_entity_name' => $this->whenLoaded('acceptanceEntity', fn () => $this->acceptanceEntity->name),
            'acceptance_personnel' => $this->acceptance_personnel_id,
            'acceptance_personnel_name' => $this->whenLoaded('acceptancePersonnel', fn () => $this->acceptancePersonnel->name),
            'acceptance_time' => $this->acceptance_time,
            'is_checked' => $this->is_checked,
            'attachments' => $this->whenLoaded('attachments'),
            'follow_ups_tags' => $this->follow_ups_tags,
            'follow_ups' => $this->whenLoaded('followUps', fn () => LifecycleFollowUpResource::collection($this->followUps)),
            'tag_ids' => $this->whenLoaded('tags', fn () => $this->tags->pluck('id')->toArray()),
            'tags' => $this->whenLoaded('tags', fn () => $this->tags->map(fn ($t) => [
                'id' => $t->id,
                'name' => $t->name,
                'category' => $t->category,
            ])),
            'progress' => $this->progress,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
