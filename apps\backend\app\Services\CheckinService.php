<?php

namespace App\Services;

use App\Models\CheckinConfig;
use App\Models\CheckinConfigUser;
use App\Models\CheckinRecord;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CheckinService
{
    public function createConfig(array $data)
    {
        return DB::transaction(function () use ($data) {
            $data['created_by'] = Auth::id();
            $config = CheckinConfig::create($data);

            if (isset($data['user_ids']) && is_array($data['user_ids'])) {
                $config->syncUsers($data['user_ids']);
            }

            return $config->load(['users:id,nickname', 'attachable']);
        });
    }

    public function updateConfig($id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $config = CheckinConfig::findOrFail($id);
            $config->update($data);

            if (isset($data['user_ids'])) {
                $config->syncUsers($data['user_ids']);
            }

            return $config;
        });
    }

    public function deleteConfig($id)
    {
        return DB::transaction(function () use ($id) {
            $config = CheckinConfig::findOrFail($id);
            $config->users()->delete();
            return $config->delete();
        });
    }

    public function checkin(array $data)
    {
        $config = CheckinConfig::findOrFail($data['checkin_config_id']);

        // 检查用户是否有权限打卡
        $hasPermission = $config->users()->where('user_id', Auth::id())->exists();
        if (!$hasPermission) {
            throw new \Exception('您没有该打卡配置的权限');
        }

        // 计算打卡状态
        $status = $this->calculateCheckinStatus($config, $data);

        $data['user_id'] = Auth::id();
        $data['status'] = $status;
        $data['checkin_time'] = now()->timestamp;

        return CheckinRecord::create($data);
    }

    protected function calculateCheckinStatus($config, $data)
    {
        // 默认状态为正常
        $status = 1;

        // 如果需要检查位置
        if ($config->location_range && $data['location_range']) {
            // 如果超出范围，标记为异常
            if ($data['location_range'] > $config->location_range) {
                $status = 2;
            }
        }

        // 如果需要照片但未提供
        if ($config->is_photo && empty($data['attachment_id'])) {
            $status = 2;
        }

        return $status;
    }

    public function getConfigs(array $filters = [])
    {
        $query = CheckinConfig::query()
            ->with(['users:id,nickname']);

        if (isset($filters['module'])) {
            $query->where('module', $filters['module']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 15);
    }

    public function getRecords(array $filters = [])
    {
        $query = CheckinRecord::query()
            ->with(['user:id,nickname', 'config:id', 'attachment']);

        if (isset($filters['config_id'])) {
            $query->where('checkin_config_id', $filters['config_id']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['date_start'])) {
            $query->where('checkin_time', '>=', $filters['date_start']);
        }

        if (isset($filters['date_end'])) {
            $query->where('checkin_time', '<=', $filters['date_end']);
        }

        return $query->orderBy('checkin_time', 'desc')
            ->paginate($filters['per_page'] ?? 15);
    }
}
