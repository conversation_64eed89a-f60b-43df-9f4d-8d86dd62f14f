/**
 * 资产管理和生命周期管理API
 */

import request from '@/utils/http'
import type {
  Asset,
  AssetQueryParams,
  AssetFormData,
  MainAssetQueryParams,
  AssetImportTask
} from '@/types/api/asset'
import type {
  Lifecycle,
  LifecycleFollowUp,
  LifecycleFormData,
  FollowUpFormData
} from '@/types/api/lifecycle'
import type { PaginatedResponse } from '@/types/api/pagination'

// ========== 资产管理 ==========

/**
 * 获取资产列表
 */
export const getAssetList = (params?: AssetQueryParams): Promise<PaginatedResponse<Asset>> => {
  return request.get<PaginatedResponse<Asset>>({
    url: '/api/admin/assets',
    params
  })
}

/**
 * 获取可作为主设备的资产列表
 */
export const getMainAssetList = (
  params?: MainAssetQueryParams
): Promise<PaginatedResponse<Asset>> => {
  return request.get<PaginatedResponse<Asset>>({
    url: '/api/admin/assets/main-assets',
    params
  })
}

/**
 * 获取资产详情
 */
export const getAssetDetail = (id: number): Promise<Asset> => {
  return request.get<Asset>({
    url: `/api/admin/assets/${id}`
  })
}

/**
 * 创建资产
 */
export const createAsset = (data: AssetFormData): Promise<Asset> => {
  return request.post<Asset>({
    url: '/api/admin/assets',
    data
  })
}

/**
 * 更新资产
 */
export const updateAsset = (id: number, data: AssetFormData): Promise<Asset> => {
  return request.put<Asset>({
    url: `/api/admin/assets/${id}`,
    data
  })
}

/**
 * 删除资产
 */
export const deleteAsset = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/assets/${id}`
  })
}

/**
 * 导出资产模板
 */
export const exportAssetTemplate = (): Promise<Blob> => {
  return request.get<Blob>({
    url: '/api/admin/assets/export-template',
    responseType: 'blob'
  })
}

/**
 * 批量导入资产
 */
export const importAssets = (
  attachmentId: number
): Promise<{
  message: string
  task_id: number
}> => {
  return request.post<{
    message: string
    task_id: number
  }>({
    url: `/api/admin/assets/import/${attachmentId}`
  })
}

/**
 * 获取导入任务状态
 */
export const getImportTaskStatus = (taskId: number): Promise<AssetImportTask> => {
  return request.get<AssetImportTask>({
    url: `/api/admin/assets/import-tasks/${taskId}`
  })
}

// ========== 生命周期管理 ==========

/**
 * 获取生命周期列表
 */
export const getLifecycleList = (params?: {
  page?: number
  per_page?: number
  asset_id?: number
  type?: string
  start_date?: string
  end_date?: string
  initiator_id?: number
  acceptance_entity_id?: number
}): Promise<{
  data: Lifecycle[]
  meta: {
    total: number
    per_page: number
    current_page: number
    last_page: number
  }
}> => {
  return request.get<{
    data: Lifecycle[]
    meta: {
      total: number
      per_page: number
      current_page: number
      last_page: number
    }
  }>({
    url: '/api/admin/lifecycles',
    params
  })
}

/**
 * 获取生命周期详情
 */
export const getLifecycleDetail = (id: number): Promise<Lifecycle> => {
  return request.get<Lifecycle>({
    url: `/api/admin/lifecycles/${id}`
  })
}

/**
 * 创建生命周期
 */
export const createLifecycle = (data: LifecycleFormData): Promise<Lifecycle> => {
  return request.post<Lifecycle>({
    url: '/api/admin/lifecycles',
    data
  })
}

/**
 * 更新生命周期
 */
export const updateLifecycle = (
  id: number,
  data: Partial<LifecycleFormData>
): Promise<Lifecycle> => {
  return request.put<Lifecycle>({
    url: `/api/admin/lifecycles/${id}`,
    data
  })
}

/**
 * 删除生命周期
 */
export const deleteLifecycle = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/lifecycles/${id}`
  })
}

/**
 * 获取验收人员列表
 */
export const getAcceptancePersonnel = (
  entityId: number
): Promise<
  Array<{
    id: number
    name: string
    phone: string
    position?: string
    department?: string
  }>
> => {
  return request.get<
    Array<{
      id: number
      name: string
      phone: string
      position?: string
      department?: string
    }>
  >({
    url: `/api/admin/lifecycles/entities/${entityId}/acceptance-personnel`
  })
}

/**
 * 获取跟进记录详情
 */
export const getFollowUpDetail = (lifecycleId: number, id: number): Promise<LifecycleFollowUp> => {
  return request.get<LifecycleFollowUp>({
    url: `/api/admin/lifecycles/${lifecycleId}/follow-ups/${id}`
  })
}

/**
 * 创建跟进记录
 */
export const createFollowUp = (
  lifecycleId: number,
  data: FollowUpFormData
): Promise<LifecycleFollowUp> => {
  return request.post<LifecycleFollowUp>({
    url: `/api/admin/lifecycles/${lifecycleId}/follow-ups`,
    data
  })
}

/**
 * 更新跟进记录
 */
export const updateFollowUp = (
  lifecycleId: number,
  id: number,
  data: Partial<FollowUpFormData>
): Promise<LifecycleFollowUp> => {
  return request.put<LifecycleFollowUp>({
    url: `/api/admin/lifecycles/${lifecycleId}/follow-ups/${id}`,
    data
  })
}

/**
 * 删除跟进记录
 */
export const deleteFollowUp = (lifecycleId: number, id: number): Promise<void> => {
  return request.del<void>({
    url: `/api/admin/lifecycles/${lifecycleId}/follow-ups/${id}`
  })
}

/**
 * 验收生命周期
 */
export const acceptLifecycle = (id: number): Promise<Lifecycle> => {
  return request.put<Lifecycle>({
    url: `/api/admin/lifecycles/${id}/accept`,
    data: {
      is_checked: 1
    }
  })
}

/**
 * 批量复制资产
 */
export const batchCopyAssets = (ids: number[]): Promise<{ message: string }> => {
  return request.post<{ message: string }>({
    url: '/api/admin/assets/batch/copy',
    data: { ids }
  })
}

/**
 * 批量删除资产
 */
export const batchDeleteAssets = (ids: number[]): Promise<{ message: string }> => {
  return request.post<{ message: string }>({
    url: '/api/admin/assets/batch/destroy',
    data: { ids }
  })
}
