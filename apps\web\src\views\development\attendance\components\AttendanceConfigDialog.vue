<template>
  <ElDialog
    v-model="visible"
    :title="type === 'add' ? '新增考勤配置' : '编辑考勤配置'"
    width="80%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <!-- 地理位置配置 -->
      <ElDivider content-position="left">地理位置配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="打卡地点" prop="location_address">
            <ElInput v-model="formData.location_address" placeholder="请输入打卡地点地址" clearable>
              <template #append>
                <ElButton @click="showMapSelector">选择位置</ElButton>
              </template>
            </ElInput>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem label="经度" prop="location_longitude">
            <ElInputNumber
              v-model="formData.location_longitude"
              placeholder="经度"
              :precision="6"
              :min="-180"
              :max="180"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="纬度" prop="location_latitude">
            <ElInputNumber
              v-model="formData.location_latitude"
              placeholder="纬度"
              :precision="6"
              :min="-90"
              :max="90"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="打卡范围" prop="location_radius">
            <ElInput
              v-model.number="formData.location_radius"
              placeholder="请输入打卡范围"
              type="number"
              min="1"
              max="10000"
            >
              <template #suffix>
                <span style="color: #909399">米</span>
              </template>
            </ElInput>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 时间配置 -->
      <ElDivider content-position="left">时间配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="打卡时间" prop="checkin_time">
            <ElTimePicker
              v-model="formData.checkin_time"
              placeholder="请选择打卡时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 拍照配置 -->
      <ElDivider content-position="left">拍照配置</ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="需要拍照打卡" prop="require_photo">
            <ElSwitch v-model="formData.require_photo" active-text="需要" inactive-text="不需要" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 人员配置 -->
      <ElDivider content-position="left">人员配置</ElDivider>

      <ElFormItem label="参与人员" prop="participant_ids">
        <UserSelectTransfer v-model="formData.participant_ids" :users="userOptions" />
      </ElFormItem>

      <!-- 状态配置 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElRadioGroup v-model="formData.status">
              <ElRadio value="active">启用</ElRadio>
              <ElRadio value="inactive">禁用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit" :loading="submitting"> 确定 </ElButton>
      </span>
    </template>

    <!-- 地图选择器 -->
    <MapSelector
      v-model="mapSelectorVisible"
      :init-location="{
        address: formData.location_address,
        longitude: formData.location_longitude,
        latitude: formData.location_latitude
      }"
      @confirm="handleMapConfirm"
    />
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AttendanceConfigDialog' })

  // Vue 核心
  import { ref, reactive, watch } from 'vue'

  // UI 框架
  import {
    ElMessage,
    ElIcon,
    ElButton,
    ElInputNumber,
    ElTimePicker,
    ElRadioGroup,
    ElRadio,
    ElDivider,
    ElSwitch
  } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // API
  import { createCheckinConfig, updateCheckinConfig, getUserOptions } from '@/api/admin/attendance'
  import type { CheckinConfig, CheckinConfigForm, UserOption } from '@/types/api/attendance'

  // 组件
  import UserSelectTransfer from './UserSelectTransfer.vue'
  import MapSelector from '@/components/custom/map-selector/index.vue'
  import type { LocationInfo } from '@/components/custom/map-selector'

  // Props
  const props = defineProps<{
    type: 'add' | 'edit'
    data?: CheckinConfig | null
  }>()

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 表单相关
  const formRef = ref<FormInstance>()
  const submitting = ref(false)

  // 表单数据
  const formData = reactive<CheckinConfigForm>({
    lifecycle_id: 0,
    location_address: '',
    location_latitude: 0,
    location_longitude: 0,
    location_radius: 500,
    checkin_time: '08:30',
    participant_ids: [],
    require_photo: false,
    status: 'active'
  })

  // 表单规则
  const rules: FormRules = {
    location_address: [{ required: true, message: '请输入打卡地点', trigger: 'blur' }],
    location_latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
    location_longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
    location_radius: [{ required: true, message: '请输入打卡范围', trigger: 'blur' }],
    checkin_time: [{ required: true, message: '请选择打卡时间', trigger: 'change' }],
    participant_ids: [{ required: true, message: '请选择参与人员', trigger: 'change' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 数据源
  const userOptions = ref<UserOption[]>([])

  // 地图选择器
  const mapSelectorVisible = ref(false)

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      lifecycle_id: 0,
      location_address: '',
      location_latitude: 0,
      location_longitude: 0,
      location_radius: 500,
      checkin_time: '08:30',
      participant_ids: [],
      require_photo: false,
      status: 'active'
    })
  }

  // 监听props变化
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        // 赋值表单数据
        Object.assign(formData, {
          lifecycle_id: newData.lifecycle_id || 0,
          location_address: newData.location_address || '',
          location_latitude: newData.location_latitude || 0,
          location_longitude: newData.location_longitude || 0,
          location_radius: newData.location_radius || 500,
          checkin_time: newData.checkin_time || '08:30',
          participant_ids: newData.participant_ids || [],
          require_photo: newData.require_photo || false,
          status: newData.status || 'active'
        })
      } else {
        resetForm()
      }
    },
    { immediate: true }
  )

  // 监听弹窗显示状态
  watch(visible, async (newVal) => {
    if (newVal) {
      await loadUserOptions()
    }
  })

  // 加载用户选项
  const loadUserOptions = async () => {
    try {
      const users = await getUserOptions()
      userOptions.value = users || []
    } catch (error) {
      console.error('加载用户列表失败:', error)
    }
  }


  // 显示地图选择器
  const showMapSelector = () => {
    mapSelectorVisible.value = true
  }

  // 处理地图选择回调
  const handleMapConfirm = (location: LocationInfo) => {
    formData.location_address = location.address
    formData.location_latitude = location.latitude
    formData.location_longitude = location.longitude
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    resetForm()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitting.value = true

      if (props.type === 'add') {
        await createCheckinConfig(formData)
        ElMessage.success('新增成功')
      } else {
        await updateCheckinConfig(props.data!.id, formData)
        ElMessage.success('编辑成功')
      }

      emit('success')
      handleClose()
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      submitting.value = false
    }
  }
</script>

<style lang="scss" scoped>

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-divider__text) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
</style>
